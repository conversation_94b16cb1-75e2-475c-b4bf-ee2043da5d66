*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.13 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e7e5e4;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: Open Sans, ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #a8a29e;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #a8a29e;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #78716c;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #2563eb;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #78716c;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #78716c;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
  text-align: inherit;
}

::-webkit-datetime-edit {
  display: inline-flex;
}

::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2378716c' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[multiple],[size]:where(select:not([size="1"])) {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #78716c;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {
  [type='checkbox']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {
  [type='radio']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media (forced-colors: active)  {
  [type='checkbox']:indeterminate {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}

h1, .h1 {
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 700;
  line-height: 1.375;
  letter-spacing: -0.025em;
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  h1, .h1 {
    font-size: 3rem;
    line-height: 1.375;
  }
}

@media (min-width: 1024px) {
  h1, .h1 {
    font-size: 3.75rem;
    line-height: 1.375;
  }
}

@media (min-width: 1280px) {
  h1, .h1 {
    line-height: 1.375;
  }
}

/* h2, .h2 { @apply text-2xl md:text-3xl lg:text-4xl font-bold tracking-tight text-gray-600 leading-snug md:leading-snug lg:leading-snug xl:leading-snug; } */

/* h3, .h3 { @apply text-xl lg:text-2xl font-bold text-gray-600 leading-snug md:leading-snug lg:leading-snug xl:leading-snug; } */

/* h4, .h4 { @apply text-lg font-medium text-gray-800; } */

/* h5, .h5 { @apply font-medium text-gray-600 dark:text-gray-300 mb-1; } */

/* h1 strong, */

/* h2 strong, */

/* h3 strong { @apply font-bold text-primary-darker; } */

/* h1.lead { @apply font-light text-gray-700 text-4xl sm:text-5xl lg:text-6xl 2xl:text-7xl; } */

/* a { @apply transition duration-150 ease-in-out; } */

/* .content_section h1 { @apply mb-4; } */

/* .content_section h2 { @apply mb-3; } */

/* .content_section h3 { @apply mb-2; } */

/* .content_section h1 a, */

/* .content_section h2 a, */

/* .content_section h3 a { @apply text-primary hover:text-primary-hover hover:underline; } */

/* .content_section h1 b, */

/* .content_section h1 strong, */

/* .content_section h2 b, */

/* .content_section h2 strong, */

/* .content_section h3 b, */

/* .content_section h3 strong { @apply text-primary font-bold; } */

/* .content_section h4 { @apply text-lg md:text-xl font-semibold leading-7 text-primary-darker mb-2; } */

/* .content_section h1.success_heading, */

/* .content_section h2.success_heading, */

/* .content_section h3.success_heading, */

/* .content_section h4.success_heading { @apply text-green-600; } */

/* .content_section p { @apply leading-7 text-gray-600 mb-6; } */

/* .content_section p a:not(.btn):not(.btn-lg) { @apply text-primary font-semibold hover:text-primary-hover hover:underline; } */

/* .content_section img { @apply max-w-full; } */

/* .content_section ul, .content_section ol { @apply pl-4 space-y-2 mb-4; } */

/* .content_section ul { @apply list-disc marker:text-secondary; } */

/* .content_section ol { @apply list-decimal; } */

/* .content_section li { @apply text-base leading-7 text-gray-600; } */

/* .content_section table { @apply w-full align-top; } */

/* .content_section table tr { @apply align-top; } */

/* .content_section table td, .content_section table th { @apply align-top; } */

/* .content_section blockquote { @apply bg-gray-100 border-l-4 border-gray-300 text-xl italic font-medium leading-relaxed p-4 my-4; } */

/* .content_section blockquote p { @apply m-0 before:content-['“'] after:content-['„']; } */

/* .content_section blockquote blockquote { @apply bg-gray-200 border-gray-400; } */

/* .content_section_sm p { @apply text-sm leading-6 mb-4; } */

/* .content_section_sm li { @apply text-sm leading-5; } */

/* .content_section_sm h2 { @apply text-2xl md:text-3xl lg:text-3xl font-bold tracking-tight text-gray-600; } */

/* .content_section_faded p, */

/* .light .content_section_faded p { @apply text-gray-500; } */

/* .dark .content_section h2, */

/* .dark .content_section h3 { @apply text-white; } */

/* .dark .content_section p { @apply text-gray-50; } */

/* .dark .content_section h4 { @apply text-primary-lighter; } */

/* .dark .content_section blockquote { @apply bg-gray-700 border-primary; } */

/* .dark .content_section blockquote blockquote { @apply bg-gray-600 border-primary-lighter; } */

/* .dark .content_section_faded p { @apply text-gray-700; } */

/* .light .content_section h2 { @apply text-gray-600; } */

/* .light .content_section h3 { @apply text-gray-500; } */

/* .light .content_section p { @apply text-gray-600; } */

/* .light .content_section h4 { @apply text-primary-darker; } */

/* .light .content_section blockquote { @apply bg-gray-100 border-gray-300; } */

/* .light .content_section blockquote blockquote { @apply bg-gray-200 border-gray-400; } */

/* .content_section.p-lg p { @apply text-lg; } */

/* .content_section.p-xl p { @apply text-xl; } */

/* div:has(.prose) { @apply */

/*     prose-headings:leading-snug */

/*     prose-h1:text-4xl */

/*     prose-headings:text-balance */

/*     md:prose-h1:text-5xl */

/*     lg:prose-h1:text-6xl */

/*     prose-h2:text-2xl */

/*     md:prose-h2:text-3xl */

/*     lg:prose-h2:text-4xl */

/*     prose-h3:text-xl */

/*     md:prose-h3:text-2xl */

/*     prose-h4:text-lg */

/*     md:prose-h4:text-lg */

/*     prose-h4:font-semibold */

/*     prose-lead:font-light */

/*     prose-img:my-0; */

/* } */

/* .sidebar-item div:has(.prose) { @apply */

/*     prose-h3:text-lg */

/*     md:prose-h3:text-xl */

/* } */

/* .secondary-title-color div:has(.prose) { @apply prose-strong:text-secondary prose-h4:text-secondary dark:prose-strong:text-secondary dark:prose-h4:text-secondary; } */

/* .fr-img-caption .fr-inner { @apply text-xs text-gray-500; } */

/* .fr-img-caption .fr-img-wrap img { @apply mb-0; } */

/* img.fr-dii.fr-fil, */

/* span.fr-img-caption.fr-dii.fr-fil { @apply mr-4; } */

/* img.fr-dii.fr-fir, */

/* span.fr-img-caption.fr-dii.fr-fir { @apply ml-4; } */

/* img.fr-dib, */

/* span.fr-img-caption.fr-dib { @apply my-6; } */

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1rem;
  padding-left: 1rem;
}

@media (min-width: 425px) {
  .container {
    max-width: 425px;
  }
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.aspect-h-3 {
  --tw-aspect-h: 3;
}

.aspect-w-4 {
  position: relative;
  padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
  --tw-aspect-w: 4;
}

.aspect-w-4 > * {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.prose {
  color: var(--tw-prose-body);
  max-width: 65ch;
}

.prose :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-links);
  text-decoration: underline;
  font-weight: 500;
}

.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)):hover {
  color: #EE7402;
}

.prose :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-bold);
  font-weight: 600;
}

.prose :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}

.prose :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}

.prose :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}

.prose :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}

.prose :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}

.prose :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}

.prose :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}

.prose :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}

.prose :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}

.prose :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
}

.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}

.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters);
}

.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  color: var(--tw-prose-bullets);
}

.prose :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em;
}

.prose :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
}

.prose :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-inline-start-width: 0.25rem;
  border-inline-start-color: var(--tw-prose-quote-borders);
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1em;
}

.prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: open-quote;
}

.prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: close-quote;
}

.prose :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 1rem;
  line-height: 1.1111111;
}

.prose :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit;
}

.prose :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 0.75rem;
  line-height: 1.3333333;
}

.prose :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit;
}

.prose :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.prose :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}

.prose :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.prose :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}

.prose :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  display: block;
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  padding-inline-end: 0.375em;
  padding-bottom: 0.1875em;
  padding-inline-start: 0.375em;
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em;
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: "`";
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: "`";
}

.prose :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.875em;
}

.prose :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.9em;
}

.prose :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-inline-end: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-inline-start: 1.1428571em;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: none;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: none;
}

.prose :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  width: 100%;
  table-layout: auto;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}

.prose :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}

.prose :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}

.prose :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders);
}

.prose :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 0;
}

.prose :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: baseline;
}

.prose :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}

.prose :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: top;
}

.prose :where(th, td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  text-align: start;
}

.prose :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}

.prose {
  --tw-prose-body: #374151;
  --tw-prose-headings: #111827;
  --tw-prose-lead: #4b5563;
  --tw-prose-links: #111827;
  --tw-prose-bold: #111827;
  --tw-prose-counters: #6b7280;
  --tw-prose-bullets: #d1d5db;
  --tw-prose-hr: #e5e7eb;
  --tw-prose-quotes: #111827;
  --tw-prose-quote-borders: #e5e7eb;
  --tw-prose-captions: #6b7280;
  --tw-prose-kbd: #111827;
  --tw-prose-kbd-shadows: 17 24 39;
  --tw-prose-code: #111827;
  --tw-prose-pre-code: #e5e7eb;
  --tw-prose-pre-bg: #1f2937;
  --tw-prose-th-borders: #d1d5db;
  --tw-prose-td-borders: #e5e7eb;
  --tw-prose-invert-body: #d1d5db;
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: #9ca3af;
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: #9ca3af;
  --tw-prose-invert-bullets: #4b5563;
  --tw-prose-invert-hr: #374151;
  --tw-prose-invert-quotes: #f3f4f6;
  --tw-prose-invert-quote-borders: #374151;
  --tw-prose-invert-captions: #9ca3af;
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: #d1d5db;
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: #4b5563;
  --tw-prose-invert-td-borders: #374151;
  font-size: 1rem;
  line-height: 1.75;
}

.prose :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}

.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}

.prose :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  padding-inline-start: 1.625em;
}

.prose :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.5714286em;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}

.prose :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose :where(h1 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
}

.prose :where(h2 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
}

.prose :where(h3 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
}

.prose :where(h4 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
}

.prose-sm {
  font-size: 0.875rem;
  line-height: 1.7142857;
}

.prose-sm :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
}

.prose-sm :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2857143em;
  line-height: 1.5555556;
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}

.prose-sm :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-inline-start: 1.1111111em;
}

.prose-sm :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 2.1428571em;
  margin-top: 0;
  margin-bottom: 0.8em;
  line-height: 1.2;
}

.prose-sm :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.4285714em;
  margin-top: 1.6em;
  margin-bottom: 0.8em;
  line-height: 1.4;
}

.prose-sm :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2857143em;
  margin-top: 1.5555556em;
  margin-bottom: 0.4444444em;
  line-height: 1.5555556;
}

.prose-sm :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.4285714em;
  margin-bottom: 0.5714286em;
  line-height: 1.4285714;
}

.prose-sm :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-sm :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  border-radius: 0.3125rem;
  padding-top: 0.1428571em;
  padding-inline-end: 0.3571429em;
  padding-bottom: 0.1428571em;
  padding-inline-start: 0.3571429em;
}

.prose-sm :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
}

.prose-sm :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
}

.prose-sm :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
}

.prose-sm :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.6666667;
  margin-top: 1.6666667em;
  margin-bottom: 1.6666667em;
  border-radius: 0.25rem;
  padding-top: 0.6666667em;
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}

.prose-sm :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
  padding-inline-start: 1.5714286em;
}

.prose-sm :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
  padding-inline-start: 1.5714286em;
}

.prose-sm :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.2857143em;
  margin-bottom: 0.2857143em;
}

.prose-sm :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4285714em;
}

.prose-sm :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4285714em;
}

.prose-sm :where(.prose-sm > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5714286em;
  margin-bottom: 0.5714286em;
}

.prose-sm :where(.prose-sm > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}

.prose-sm :where(.prose-sm > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.1428571em;
}

.prose-sm :where(.prose-sm > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}

.prose-sm :where(.prose-sm > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.1428571em;
}

.prose-sm :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5714286em;
  margin-bottom: 0.5714286em;
}

.prose-sm :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
}

.prose-sm :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}

.prose-sm :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.2857143em;
  padding-inline-start: 1.5714286em;
}

.prose-sm :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2.8571429em;
  margin-bottom: 2.8571429em;
}

.prose-sm :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.5;
}

.prose-sm :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}

.prose-sm :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-sm :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-sm :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.6666667em;
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}

.prose-sm :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-sm :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-sm :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-sm :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.3333333;
  margin-top: 0.6666667em;
}

.prose-sm :where(.prose-sm > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(.prose-sm > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-xl {
  font-size: 1.25rem;
  line-height: 1.8;
}

.prose-xl :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

.prose-xl :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2em;
  line-height: 1.5;
  margin-top: 1em;
  margin-bottom: 1em;
}

.prose-xl :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1.0666667em;
}

.prose-xl :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 2.8em;
  margin-top: 0;
  margin-bottom: 0.8571429em;
  line-height: 1;
}

.prose-xl :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.8em;
  margin-top: 1.5555556em;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}

.prose-xl :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.5em;
  margin-top: 1.6em;
  margin-bottom: 0.6666667em;
  line-height: 1.3333333;
}

.prose-xl :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.8em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}

.prose-xl :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose-xl :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose-xl :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-xl :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose-xl :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
  border-radius: 0.3125rem;
  padding-top: 0.25em;
  padding-inline-end: 0.4em;
  padding-bottom: 0.25em;
  padding-inline-start: 0.4em;
}

.prose-xl :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
}

.prose-xl :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8611111em;
}

.prose-xl :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
}

.prose-xl :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
  line-height: 1.7777778;
  margin-top: 2em;
  margin-bottom: 2em;
  border-radius: 0.5rem;
  padding-top: 1.1111111em;
  padding-inline-end: 1.3333333em;
  padding-bottom: 1.1111111em;
  padding-inline-start: 1.3333333em;
}

.prose-xl :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
  margin-bottom: 1.2em;
  padding-inline-start: 1.6em;
}

.prose-xl :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
  margin-bottom: 1.2em;
  padding-inline-start: 1.6em;
}

.prose-xl :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.6em;
  margin-bottom: 0.6em;
}

.prose-xl :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4em;
}

.prose-xl :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4em;
}

.prose-xl :where(.prose-xl > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.8em;
  margin-bottom: 0.8em;
}

.prose-xl :where(.prose-xl > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
}

.prose-xl :where(.prose-xl > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.2em;
}

.prose-xl :where(.prose-xl > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
}

.prose-xl :where(.prose-xl > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.2em;
}

.prose-xl :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.8em;
  margin-bottom: 0.8em;
}

.prose-xl :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

.prose-xl :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
}

.prose-xl :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.6em;
  padding-inline-start: 1.6em;
}

.prose-xl :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2.8em;
  margin-bottom: 2.8em;
}

.prose-xl :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
  line-height: 1.5555556;
}

.prose-xl :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0.6666667em;
  padding-bottom: 0.8888889em;
  padding-inline-start: 0.6666667em;
}

.prose-xl :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-xl :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-xl :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.8888889em;
  padding-inline-end: 0.6666667em;
  padding-bottom: 0.8888889em;
  padding-inline-start: 0.6666667em;
}

.prose-xl :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-xl :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-xl :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose-xl :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-xl :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
  line-height: 1.5555556;
  margin-top: 1em;
}

.prose-xl :where(.prose-xl > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(.prose-xl > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-sky {
  --tw-prose-links: #0284c7;
  --tw-prose-invert-links: #0ea5e9;
}

.prose-primary {
  --tw-prose-body: #000;
  --tw-prose-headings: #57534e;
  --tw-prose-lead: #44403c;
  --tw-prose-links: #EE7402;
  --tw-prose-bold: #44403c;
  --tw-prose-counters: #EE7402;
  --tw-prose-bullets: #F39B4E;
  --tw-prose-hr: #d6d3d1;
  --tw-prose-quotes: #44403c;
  --tw-prose-quote-borders: #F5B47D;
  --tw-prose-captions: #a8a29e;
  --tw-prose-code: #7F3701;
  --tw-prose-pre-code: #f5f5f4;
  --tw-prose-pre-bg: #292524;
  --tw-prose-th-borders: #d6d3d1;
  --tw-prose-td-borders: #e7e5e4;
}

.prose-primary :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.prose-primary :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.prose-primary :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.prose-primary :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.prose-primary :where(h1 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.prose-primary :where(h2 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.prose-primary :where(h3 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.prose-primary :where(h4 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.prose-primary :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.prose-primary :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #000;
}

.prose-primary :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #000;
}

.prose-primary :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #000;
}

.prose-primary :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)):hover {
  color: #CC5F02;
}

.btn-primary.btn-filled {
  background-color: #EE7402;
  color: #fff;
}

.btn-primary.btn-filled:hover {
  background-color: #F39B4E;
}

.btn-primary.btn-filled:focus-visible {
  outline-color: #EE7402;
}

.btn-primary.btn-outlined {
  color: #EE7402;
  border-width: 1px;
  border-color: #EE7402;
}

.btn-primary.btn-outlined:hover {
  background-color: #EE7402;
  color: #fff;
}

.btn-primary.btn-tonal {
  background-color: #FADBC9;
  color: #CC5F02;
}

.btn-primary.btn-tonal:hover {
  background-color: #F8CBA8;
  color: #CC5F02;
}

.btn-primary.btn-tonal:focus-visible {
  outline-color: #F8CBA8;
}

.btn-primary.btn-link {
  background-color: transparent;
  color: #F39B4E;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.btn-primary.btn-link:hover {
  background-color: transparent;
  color: #EE7402;
}

.btn-primary.btn-link:focus-visible {
  outline-color: #F8CBA8;
}

.btn-secondary.btn-filled {
  background-color: #1bff00;
  color: #fff;
}

.btn-secondary.btn-filled:hover {
  background-color: #0cd300;
}

.btn-secondary.btn-filled:focus-visible {
  outline-color: #1bff00;
}

.btn-secondary.btn-outlined {
  color: #1bff00;
  border-width: 1px;
  border-color: #1bff00;
}

.btn-secondary.btn-outlined:hover {
  background-color: #1bff00;
  color: #fff;
}

.btn-secondary.btn-tonal {
  background-color: #a8ff80;
  color: #099f00;
}

.btn-secondary.btn-tonal:hover {
  background-color: #6eff37;
  color: #099f00;
}

.btn-secondary.btn-tonal:focus-visible {
  outline-color: #6eff37;
}

.btn-secondary.btn-link {
  background-color: transparent;
  color: #1bff00;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.btn-secondary.btn-link:hover {
  background-color: transparent;
  color: #0cd300;
}

.btn-secondary.btn-link:focus-visible {
  outline-color: #6eff37;
}

.btn-grayscale.btn-filled {
  background-color: #78716c;
  color: #fff;
}

.btn-grayscale.btn-filled:hover {
  background-color: #57534e;
}

.btn-grayscale.btn-filled:focus-visible {
  outline-color: #78716c;
}

.btn-grayscale.btn-outlined {
  color: #78716c;
  border-width: 1px;
  border-color: #78716c;
}

.btn-grayscale.btn-outlined:hover {
  background-color: #78716c;
  color: #fff;
}

.btn-grayscale.btn-tonal {
  background-color: #e7e5e4;
  color: #44403c;
}

.btn-grayscale.btn-tonal:hover {
  background-color: #d6d3d1;
  color: #44403c;
}

.btn-grayscale.btn-tonal:focus-visible {
  outline-color: #d6d3d1;
}

.btn-grayscale.btn-link {
  background-color: transparent;
  color: #78716c;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.btn-grayscale.btn-link:hover {
  background-color: transparent;
  color: #57534e;
}

.btn-grayscale.btn-link:focus-visible {
  outline-color: #d6d3d1;
}

.btn-error.btn-filled {
  background-color: #ef4444;
  color: #fff;
}

.btn-error.btn-filled:hover {
  background-color: #dc2626;
}

.btn-error.btn-filled:focus-visible {
  outline-color: #ef4444;
}

.btn-error.btn-outlined {
  color: #dc2626;
  border-width: 1px;
  border-color: #dc2626;
}

.btn-error.btn-outlined:hover {
  background-color: #dc2626;
  color: #fff;
}

.btn-error.btn-tonal {
  background-color: #fecaca;
  color: #b91c1c;
}

.btn-error.btn-tonal:hover {
  background-color: #fca5a5;
  color: #b91c1c;
}

.btn-error.btn-tonal:focus-visible {
  outline-color: #fca5a5;
}

.btn-error.btn-link {
  background-color: transparent;
  color: #ef4444;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.btn-error.btn-link:hover {
  background-color: transparent;
  color: #dc2626;
}

.btn-error.btn-link:focus-visible {
  outline-color: #fca5a5;
}

.btn-info.btn-filled {
  background-color: #06b6d4;
  color: #fff;
}

.btn-info.btn-filled:hover {
  background-color: #0891b2;
}

.btn-info.btn-filled:focus-visible {
  outline-color: #06b6d4;
}

.btn-info.btn-outlined {
  color: #0891b2;
  border-width: 1px;
  border-color: #0891b2;
}

.btn-info.btn-outlined:hover {
  background-color: #0891b2;
  color: #fff;
}

.btn-info.btn-tonal {
  background-color: #a5f3fc;
  color: #0e7490;
}

.btn-info.btn-tonal:hover {
  background-color: #67e8f9;
  color: #0e7490;
}

.btn-info.btn-tonal:focus-visible {
  outline-color: #67e8f9;
}

.btn-info.btn-link {
  background-color: transparent;
  color: #06b6d4;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.btn-info.btn-link:hover {
  background-color: transparent;
  color: #0891b2;
}

.btn-info.btn-link:focus-visible {
  outline-color: #67e8f9;
}

.btn-white.btn-filled {
  color: #F39B4E;
  background-color: #fff;
}

.btn-white.btn-filled:hover {
  color: #EE7402;
}

.btn-white.btn-filled:focus-visible {
  outline-color: #fff;
}

.btn-white.btn-outlined {
  color: #fff;
  border-width: 1px;
  border-color: #fff;
}

.btn-white.btn-outlined:hover {
  background-color: #fff;
  color: #EE7402;
}

.btn-white.btn-tonal {
  background-color: rgba(255,255,255,.7);
  color: #CC5F02;
}

.btn-white.btn-tonal:hover {
  background-color: #fff;
  color: #CC5F02;
}

.btn-white.btn-tonal:focus-visible {
  outline-color: rgba(255,255,255,.4);
}

.btn-white.btn-link {
  background-color: transparent;
  color: #fff;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.btn-white.btn-link:hover {
  background-color: transparent;
  color: #f5f5f4;
}

.btn-white.btn-link:focus-visible {
  outline-color: #f5f5f4;
}

.btn-success.btn-filled {
  background-color: #16a34a;
  color: #fff;
}

.btn-success.btn-filled:hover {
  background-color: #22c55e;
}

.btn-success.btn-filled:focus-visible {
  outline-color: #16a34a;
}

.btn-success.btn-outlined {
  color: #16a34a;
  border-width: 1px;
  border-color: #16a34a;
}

.btn-success.btn-outlined:hover {
  background-color: #16a34a;
  color: #fff;
}

.btn-success.btn-tonal {
  background-color: #bbf7d0;
  color: #15803d;
}

.btn-success.btn-tonal:hover {
  background-color: #86efac;
  color: #15803d;
}

.btn-success.btn-tonal:focus-visible {
  outline-color: #86efac;
}

.btn-success.btn-link {
  background-color: transparent;
  color: #22c55e;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.btn-success.btn-link:hover {
  background-color: transparent;
  color: #16a34a;
}

.btn-success.btn-link:focus-visible {
  outline-color: #86efac;
}

.button_wrapper {
  margin-top: 2.5rem;
  align-items: center;
}

.button_wrapper > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.btn-sm {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
}

@media (min-width: 768px) {
  .btn-sm {
    display: inline-flex;
    width: auto;
    justify-content: flex-start;
  }
}

.btn {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-weight: 600;
}

@media (min-width: 768px) {
  .btn {
    display: inline-flex;
    width: auto;
    justify-content: flex-start;
  }
}

.btn-lg {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  padding-left: 3rem;
  padding-right: 3rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
}

@media (min-width: 768px) {
  .btn-lg {
    display: inline-flex;
    width: auto;
    justify-content: flex-start;
  }
}

.btn-sm:focus-visible, .btn:focus-visible, .btn-lg:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
}

header[data-section="navbar"] .btn {
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

@media (min-width: 768px) {
  header[data-section="navbar"] .btn {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 1280px) {
  header[data-section="navbar"] .btn {
    padding-left: 2rem;
    padding-right: 2rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.btn-rounded {
  border-radius: 0.375rem;
}

.btn-sm.btn-rounded {
  border-radius: 0.25rem;
}

.btn-pill {
  border-radius: 999px;
}

.btn-elevated {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-filled.btn-primary,
.btn-elevated.btn-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(238 116 2 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-filled.btn-primary:hover,
.btn-elevated.btn-primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(204 95 2 / var(--tw-bg-opacity));
}

.btn-filled.btn-primary:focus-visible,
.btn-elevated.btn-primary:focus-visible {
  outline-color: #CC5F02;
}

.btn-tonal.btn-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(250 219 201 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(129 59 13 / var(--tw-text-opacity));
}

.btn-tonal.btn-primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(248 203 168 / var(--tw-bg-opacity));
}

.btn-tonal.btn-primary:focus-visible {
  outline-color: #F8CBA8;
}

.btn-outlined.btn-primary {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(238 116 2 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

.btn-outlined.btn-primary:hover {
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(238 116 2 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-link.btn-primary {
  border-radius: 0px;
  padding: 0px;
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-link.btn-primary:hover {
  --tw-text-opacity: 1;
  color: rgb(204 95 2 / var(--tw-text-opacity));
}

.btn-filled.btn-secondary,
.btn-elevated.btn-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(8 99 0 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-filled.btn-secondary:hover,
.btn-elevated.btn-secondary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(10 123 0 / var(--tw-bg-opacity));
}

.btn-filled.btn-secondary:focus-visible,
.btn-elevated.btn-secondary:focus-visible {
  outline-color: #003b01;
}

.btn-tonal.btn-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(231 229 228 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(0 59 1 / var(--tw-text-opacity));
}

.btn-tonal.btn-secondary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(110 255 55 / var(--tw-bg-opacity));
}

.btn-tonal.btn-secondary:focus-visible {
  outline-color: #6eff37;
}

.btn-outlined.btn-secondary {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(8 99 0 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(8 99 0 / var(--tw-text-opacity));
}

.btn-outlined.btn-secondary:hover {
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(8 99 0 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-link.btn-secondary {
  border-radius: 0px;
  padding: 0px;
  --tw-text-opacity: 1;
  color: rgb(8 99 0 / var(--tw-text-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-link.btn-secondary:hover {
  --tw-text-opacity: 1;
  color: rgb(10 123 0 / var(--tw-text-opacity));
}

.btn-filled.btn-grayscale,
.btn-elevated.btn-grayscale {
  --tw-bg-opacity: 1;
  background-color: rgb(120 113 108 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-filled.btn-grayscale:hover,
.btn-elevated.btn-grayscale:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(87 83 78 / var(--tw-bg-opacity));
}

.btn-filled.btn-grayscale:focus-visible,
.btn-elevated.btn-grayscale:focus-visible {
  outline-color: #57534e;
}

.btn-tonal.btn-grayscale {
  --tw-bg-opacity: 1;
  background-color: rgb(231 229 228 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(68 64 60 / var(--tw-text-opacity));
}

.btn-tonal.btn-grayscale:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(214 211 209 / var(--tw-bg-opacity));
}

.btn-tonal.btn-grayscale:focus-visible {
  outline-color: #d6d3d1;
}

.btn-outlined.btn-grayscale {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(120 113 108 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(120 113 108 / var(--tw-text-opacity));
}

.btn-outlined.btn-grayscale:hover {
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(120 113 108 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-link.btn-grayscale {
  border-radius: 0px;
  padding: 0px;
  --tw-text-opacity: 1;
  color: rgb(120 113 108 / var(--tw-text-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-link.btn-grayscale:hover {
  --tw-text-opacity: 1;
  color: rgb(87 83 78 / var(--tw-text-opacity));
}

.btn-filled.btn-error,
.btn-elevated.btn-error {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-filled.btn-error:hover,
.btn-elevated.btn-error:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}

.btn-filled.btn-error:focus-visible,
.btn-elevated.btn-error:focus-visible {
  outline-color: #dc2626;
}

.btn-tonal.btn-error {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity));
}

.btn-tonal.btn-error:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(252 165 165 / var(--tw-bg-opacity));
}

.btn-tonal.btn-error:focus-visible {
  outline-color: #fca5a5;
}

.btn-outlined.btn-error {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}

.btn-outlined.btn-error:hover {
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-link.btn-error {
  border-radius: 0px;
  padding: 0px;
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-link.btn-error:hover {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}

.btn-filled.btn-warning,
.btn-elevated.btn-warning {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-filled.btn-warning:hover,
.btn-elevated.btn-warning:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity));
}

.btn-filled.btn-warning:focus-visible,
.btn-elevated.btn-warning:focus-visible {
  outline-color: #ea580c;
}

.btn-tonal.btn-warning {
  --tw-bg-opacity: 1;
  background-color: rgb(254 215 170 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity));
}

.btn-tonal.btn-warning:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(253 186 116 / var(--tw-bg-opacity));
}

.btn-tonal.btn-warning:focus-visible {
  outline-color: #fdba74;
}

.btn-outlined.btn-warning {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity));
}

.btn-outlined.btn-warning:hover {
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-link.btn-warning {
  border-radius: 0px;
  padding: 0px;
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-link.btn-warning:hover {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity));
}

.btn-filled.btn-info,
.btn-elevated.btn-info {
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-filled.btn-info:hover,
.btn-elevated.btn-info:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(8 145 178 / var(--tw-bg-opacity));
}

.btn-filled.btn-info:focus-visible,
.btn-elevated.btn-info:focus-visible {
  outline-color: #0891b2;
}

.btn-tonal.btn-info {
  --tw-bg-opacity: 1;
  background-color: rgb(165 243 252 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(14 116 144 / var(--tw-text-opacity));
}

.btn-tonal.btn-info:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(103 232 249 / var(--tw-bg-opacity));
}

.btn-tonal.btn-info:focus-visible {
  outline-color: #67e8f9;
}

.btn-outlined.btn-info {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(6 182 212 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(6 182 212 / var(--tw-text-opacity));
}

.btn-outlined.btn-info:hover {
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-link.btn-info {
  border-radius: 0px;
  padding: 0px;
  --tw-text-opacity: 1;
  color: rgb(6 182 212 / var(--tw-text-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-link.btn-info:hover {
  --tw-text-opacity: 1;
  color: rgb(8 145 178 / var(--tw-text-opacity));
}

.btn-filled.btn-white,
.btn-elevated.btn-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

.btn-filled.btn-white:hover,
.btn-elevated.btn-white:hover {
  --tw-text-opacity: 1;
  color: rgb(204 95 2 / var(--tw-text-opacity));
}

.btn-filled.btn-white:focus-visible,
.btn-elevated.btn-white:focus-visible {
  outline-color: #CC5F02;
}

.btn-tonal.btn-white {
  background-color: rgb(255 255 255 / 0.7);
  --tw-text-opacity: 1;
  color: rgb(129 59 13 / var(--tw-text-opacity));
}

.btn-tonal.btn-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.btn-tonal.btn-white:focus-visible {
  outline-color: #F8CBA8;
}

.btn-outlined.btn-white {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

.btn-outlined.btn-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(204 95 2 / var(--tw-text-opacity));
}

.btn-link.btn-white {
  border-radius: 0px;
  padding: 0px;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-link.btn-white:hover {
  --tw-text-opacity: 1;
  color: rgb(245 245 244 / var(--tw-text-opacity));
}

.btn-primary-default {
  display: inline-block;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(238 116 2 / var(--tw-bg-opacity));
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-primary-default:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(204 95 2 / var(--tw-bg-opacity));
}

.btn-primary-default:focus-visible {
  outline-color: #CC5F02;
}

.btn-secondary-default {
  display: inline-block;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(8 99 0 / var(--tw-bg-opacity));
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-secondary-default:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(10 123 0 / var(--tw-bg-opacity));
}

.btn-secondary-default:focus-visible {
  outline-color: #003b01;
}

.btn-white-default {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

.btn-white-default:hover {
  --tw-text-opacity: 1;
  color: rgb(204 95 2 / var(--tw-text-opacity));
}

.btn-white-default:focus-visible {
  outline-color: #CC5F02;
}

/* .container { @apply xl:max-w-screen-xl; } */

/* .container.container-xl { @apply xl:max-w-screen-2xl; } */

/* .footer { @apply border-t border-gray-300 py-10 md:py-4; } */

/* .footer-bottom { @apply py-2 border-t-2 border-gray-300; } */

/* footer .footer-row { @apply gap-x-10 gap-y-6; } */

/* footer .social-icons-row { @apply gap-x-10 md:gap-x-14 lg:gap-x-4 mt-4 lg:mt-0; } */

/* footer .social-media a { @apply text-2xl md:text-xl lg:text-lg text-gray-600 hover:text-gray-800 leading-none; } */

/* footer .navbar-logo img { @apply max-h-12; } */

/* footer .copyright, */

/* footer .legal { @apply space-y-3 md:space-y-0 mt-4 md:mt-0; } */

/* footer .copyright a, */

/* footer .legal a { @apply hover:text-gray-900; } */

/* footer .copyright-light, */

/* footer .legal-light { @apply text-gray-400; } */

/* footer .footer-row .footer-content { @apply md:mx-auto space-y-4 md:max-w-[66.666%] lg:max-w-full; } */

/* footer .footermenu .menu-vertical a { @apply capitalize; } */

/* footer .footermenu .menu-header { @apply flex uppercase font-semibold py-2 text-gray-600 hover:text-gray-800 leading-none; } */

footer .footer-contact > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

footer .footer-contact {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

footer .footer-contact a {
  display: flex;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-weight: 600;
  text-transform: uppercase;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

footer .footer-contact a:hover {
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

/* footer .footer-contact .footer-adres { @apply text-gray-600 py-2 space-y-2; } */

/* footer .footer-contact .footer-adres a { @apply lowercase; } */

/* footer .subscribe .footer-input-group { @apply flex; } */

/* footer .subscribe input { @apply py-2.5 rounded-l border-y-2 border-l-2 leading-none border-gray-300 focus:ring-0 placeholder-gray-300; } */

/* footer .subscribe button { @apply text-white uppercase rounded-r bg-gray-600 hover:bg-gray-500 py-2.5 px-2 md:px-4 leading-none; } */

/* footer .footer-row .footer-content { @apply text-gray-400; } */

/* footer .footer-row .buttons { @apply pt-4 lg:pt-0 space-y-12 md:space-y-0 md:space-x-12; } */

/* footer .footer-row .button { @apply text-white uppercase rounded bg-gray-600 hover:bg-gray-500 py-2.5 px-2 md:px-4; } */

/* footer .footer-row .subscribe-title { @apply uppercase font-semibold py-2 text-gray-600 hover:text-gray-800 leading-none; } */

/* footer .footer-row .subscribe-content { @apply mt-3 text-gray-500; } */

/* footer .footer-row .spacer { @apply w-2/3; } */

/* #footer-3 .social-icons-row { @apply gap-x-14 md:gap-x-10 ; } */

/* #footer-5 .footer .footer-row { @apply lg:-mx-8 gap-x-0 divide-y-2 md:divide-y-0 lg:divide-x-2 divide-solid divide-gray-300; } */

/* #footer-5 .footer-content, */

/* #footer-5 .footermenu, */

/* #footer-5 .footer-contact { @apply px-4 md:px-6 lg:px-8; } */

/* #footer-5 .footermenu, */

/* #footer-5 .logo, */

/* #footer-5 .footer-contact { @apply py-4; } */

/* #footer-5 .footer-contact { @apply pl-8 lg:pl-12 xl:pl-16; } */

/* #footer-7 .footer-row { @apply py-4 gap-x-10 gap-y-10; } */

/* #footer-8 .footer-row { @apply  space-y-4 md:-space-y-0 md:gap-x-4 xl:gap-x-10; } */

/* #footer-8 .subscribe-large { @apply md:border-t md:border-gray-300 pt-3 xl:border-none xl:pt-0; } */

/* #footer-8 .footer-row .subscribe-title { @apply md:pb-0 xl:pb-2; } */

/* #footer-8 .footer-row .subscribe-content { @apply md:mt-0 xl:mt-3; } */

/* #footer-8 .footer-row .subscribe { @apply mt-4 md:mt-0 xl:mt-4; } */

/* #footer-9 .footer-row .footer-content { @apply lg:w-1/2 xl:w-1/3; } */

/* Navbar menu's */

/* .mainmenu { @apply relative z-40; } */

/* .mainmenu > ul { @apply md:gap-x-8 lg:gap-x-12; } */

/* .mainmenu > ul > li { @apply relative; } */

/* .mainmenu > ul > li > p { @apply flex py-6 text-gray-600 hover:text-gray-800 leading-none; } */

/* .mainmenu > ul > li > a { @apply flex py-6 text-gray-600 hover:text-gray-800 leading-none; } */

/* .mainmenu > ul > li.active > a { @apply text-gray-900 hover:text-gray-900 font-medium; } */

/* .mainmenu > ul > li > ul { @apply hidden absolute top-full bg-white -translate-x-4 rounded-b w-64 shadow overflow-hidden; } */

/* .mainmenu > ul > li:hover > ul { @apply block divide-y divide-gray-100 z-50; } */

/* .mainmenu > ul > li > ul > li > a { @apply block py-2 px-4 text-sm text-gray-500 hover:text-gray-700; } */

/* .navbar-bottom .mainmenu ul li a { @apply text-gray-500 hover:text-gray-700; } */

/* Footer menu's */

/* .footermenu > ul { @apply md:flex flex-wrap gap-x-8 lg:gap-x-12 gap-y-2 md:justify-center xl:justify-end space-y-4 md:space-y-0; } */

/* .footermenu > ul > li > a { @apply text-center md:text-left block py-2 text-gray-600 hover:text-gray-800 leading-none; } */

/* Mobile menu's */

/* .mobilemenu-wrapper { @apply fixed inset-0 bg-gray-700/40; } */

/* .mobilemenu-inner { @apply absolute right-0 inset-y-0 w-4/5 md:w-1/3 bg-white shadow p-4; } */

/* .mobilemenu-wrapper .close-button { @apply text-gray-500 inline-flex justify-center items-center; } */

/* .mobilemenu-content { @apply pt-6 flow-root; } */

/* .mobilemenu-content .mobilemenu { @apply -my-6 divide-y divide-gray-500/10; } */

/* .mobilemenu > ul { @apply space-y-2 py-6 px-2; } */

/* .mobilemenu > ul > li { @apply -mx-3 block; } */

/* .mobilemenu > ul > li.hasSubmenu > .mobilemenu-item { @apply flex justify-between gap-x-3; } */

/* .mobilemenu > ul > li > a, */

/* .mobilemenu > ul > li > .mobilemenu-item > a { @apply block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900; } */

/* .mobilemenu > ul > li.active > a { @apply bg-gray-100; } */

/* .mobilemenu > ul > li > ul.submenu { @apply space-y-3 pt-1 pb-3 px-3; } */

/* .mobilemenu-contact { @apply mt-auto -mx-4 px-4 py-6 text-gray-500 leading-7 space-y-6 bg-gray-100; } */

/* .mobilemenu-contact .navbar-logo img { @apply max-h-10; } */

/* .mobilemenu-contact a { @apply text-gray-700 font-medium inline-flex items-center text-sm; } */

/* Atomic */

/* #main-nav > ul { @apply flex gap-x-2 lg:gap-x-4 xl:gap-x-6 2xl:gap-x-12; } */

/* #main-nav > ul > li { @apply self-center; } */

#main-nav > ul > li > .nav-item {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

#main-nav > ul > li > .nav-item:hover {
  --tw-text-opacity: 1;
  color: rgb(204 95 2 / var(--tw-text-opacity));
}

/* #main-nav > ul > li > span.nav-item { @apply cursor-default; } */

/* #main-nav > ul > li > .nav-item > .parent-icon { @apply text-sm leading-none; } */

#main-nav > ul > li.active > .nav-item {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(204 95 2 / var(--tw-text-opacity));
}

#main-nav > ul > li.active > .nav-item:hover {
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

/* #top-nav > ul { @apply flex gap-x-6; } */

/* #top-nav > ul > li { @apply self-center; } */

#top-nav > ul > li > .nav-item {
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

#top-nav > ul > li > .nav-item:hover {
  --tw-text-opacity: 1;
  color: rgb(204 95 2 / var(--tw-text-opacity));
}

/* #top-nav > ul > li > span.nav-item { @apply cursor-default; } */

/* #top-nav > ul > li > .nav-item > .parent-icon { @apply hidden; } */

#top-nav > ul > li.active > .nav-item {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(204 95 2 / var(--tw-text-opacity));
}

#top-nav > ul > li.active > .nav-item:hover {
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

/* #mobile-nav ul { @apply space-y-2; } */

/* #mobile-nav > ul > li { @apply flex flex-wrap w-full items-center; } */

#mobile-nav > ul > li > .nav-item {
  display: flex;
  align-items: center;
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

/* #mobile-nav > ul > li > .parent-icon, */

/* #mobile-nav > ul > li > .nav-item > .parent-icon { @apply ml-8 text-base; } */

#mobile-nav > ul > li > ul > li > .nav-item {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  #mobile-nav > ul > li > ul > li > .nav-item {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

/* #mobile-nav ul.mobile-topmenu { @apply space-y-1; } */

#mobile-nav ul.mobile-topmenu > li > .nav-item {
  padding-top: 0px;
  padding-bottom: 0px;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

/* [data-name="navbar-8"] #main-nav > ul { @apply flex gap-x-6 xl:gap-x-8 2xl:gap-x-12; } */

footer .footer-menu .nav-item {
  display: inline-flex;
  font-size: 1.125rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

footer .footer-menu .nav-item:hover {
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

.group\/menuitem:hover footer .footer-menu .nav-item {
  --tw-text-opacity: 1;
  color: rgb(41 37 36 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  footer .footer-menu .nav-item {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 1024px) {
  footer .footer-menu .nav-item {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

footer .footer-menu .nav-item:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(214 211 209 / var(--tw-text-opacity));
}

footer .footer-menu .nav-item:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(245 245 244 / var(--tw-text-opacity));
}

/* footer .footer-menu > ul { @apply flex flex-col space-y-6 md:space-y-0 md:flex-row md:items-center md:gap-x-6 lg:gap-x-8; } */

/* footer .footer-menu > ul > li { @apply text-center md:text-left; } */

/* footer[data-name="footer-4"] .footer-menu > ul, */

/* footer[data-name="footer-5"] .footer-menu > ul, */

/* footer[data-name="footer-8"] .footer-menu > ul { @apply flex flex-col md:flex-col space-y-2 md:space-y-1 items-start md:items-start; } */

/* footer[data-name="footer-4"] .footer-menu > ul { @apply items-center md:items-center; } */

/* footer[data-name="footer-8"] .footer-menu > ul > li { @apply leading-relaxed text-left md:text-left; } */

/* footer[data-name="footer-4"] .footer-menu > ul > li { @apply leading-relaxed text-center md:items-center; } */

/* footer[data-name="footer-4"] .footer-menu .nav-item, */

/* footer[data-name="footer-5"] .footer-menu .nav-item, */

/* footer[data-name="footer-8"] .footer-menu .nav-item { @apply text-base md:text-sm lg:text-base; } */

/* .navbar { @apply bg-gray-100; } */

/* .navbar-top { @apply border-b-2 border-gray-300 py-4 md:py-3; } */

/* .navbar-bottom { @apply border-t-2 border-b border-gray-300 py-3; } */

/* .navbar .navbar-row { @apply md:gap-x-8; } */

/* .navbar .logo { @apply py-4 md:py-3; } */

/* .navbar .logo img { @apply max-h-8 lg:max-h-10 object-contain object-left; } */

/* .navbar-buttons { @apply hidden md:block; } */

/* .navbar-buttons a.link { @apply hidden md:inline-block uppercase leading-none text-gray-600 hover:text-gray-800; } */

/* .navbar-buttons a.button { @apply hidden md:flex py-3 px-8 rounded-md border border-gray-500 hover:border-gray-800; } */

/* .navbar .contact-info address { @apply not-italic flex gap-x-8; } */

/* .navbar .contact-info address a { @apply inline-flex items-center justify-center md:justify-start h-8 w-8 md:h-auto md:w-auto rounded-full md:rounded-none bg-gray-700 md:bg-transparent text-white md:text-gray-600 hover:text-gray-800 leading-none; } */

/* .navbar .contact-info address a span { @apply hidden md:block ml-1.5; } */

/* .navbar-top .contact-info address { @apply not-italic flex gap-x-8; } */

.navbar-top .contact-info address a {
  display: inline-flex;
  align-items: center;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.navbar-top .contact-info address a:hover {
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

/* .navbar-top .contact-info address a span { @apply ml-1.5; } */

/* .navbar .search { @apply border-l border-gray-300 pl-4 md:pl-8; } */

/* .navbar .search-icon { @apply text-xl leading-none text-gray-400 hover:text-gray-600; } */

/* .navbar-top .social-media a { @apply text-lg text-gray-600 hover:text-gray-800 leading-none; } */

/* #navbar-5 .navbar-row { @apply gap-x-6 lg:gap-x-16; } */

/* #navbar-8 .navbar .navbar-row { @apply gap-x-4; } */

/* #navbar-9 { @apply bg-gray-50; } */

/* #navbar-9 .navbar { @apply bg-gray-50 py-4; } */

/* #navbar-9 .navbar .navbar-row { @apply gap-x-4; } */

/* #navbar-10 .navbar .navbar-row { @apply gap-x-4; } */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

.visible {
  visibility: visible;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.-inset-y-3 {
  top: -0.75rem;
  bottom: -0.75rem;
}

.inset-x-0 {
  left: 0px;
  right: 0px;
}

.inset-x-6 {
  left: 1.5rem;
  right: 1.5rem;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.-bottom-24 {
  bottom: -6rem;
}

.-left-2 {
  left: -0.5rem;
}

.-left-4 {
  left: -1rem;
}

.-top-4 {
  top: -1rem;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-12 {
  bottom: 3rem;
}

.bottom-5 {
  bottom: 1.25rem;
}

.bottom-8 {
  bottom: 2rem;
}

.left-0 {
  left: 0px;
}

.left-1\/2 {
  left: 50%;
}

.left-4 {
  left: 1rem;
}

.left-8 {
  left: 2rem;
}

.right-0 {
  right: 0px;
}

.right-1\/2 {
  right: 50%;
}

.right-2 {
  right: 0.5rem;
}

.right-8 {
  right: 2rem;
}

.top-0 {
  top: 0px;
}

.top-1\/2 {
  top: 50%;
}

.top-14 {
  top: 3.5rem;
}

.top-2 {
  top: 0.5rem;
}

.top-3\.5 {
  top: 0.875rem;
}

.top-4 {
  top: 1rem;
}

.top-6 {
  top: 1.5rem;
}

.top-8 {
  top: 2rem;
}

.top-px {
  top: 1px;
}

.top-full {
  top: 100%;
}

.isolate {
  isolation: isolate;
}

.-z-10 {
  z-index: -10;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[51\] {
  z-index: 51;
}

.z-\[9999\] {
  z-index: 9999;
}

.z-\[999\] {
  z-index: 999;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.order-first {
  order: -9999;
}

.order-last {
  order: 9999;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-10 {
  grid-column: span 10 / span 10;
}

.col-span-12 {
  grid-column: span 12 / span 12;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.col-span-5 {
  grid-column: span 5 / span 5;
}

.col-span-6 {
  grid-column: span 6 / span 6;
}

.col-span-8 {
  grid-column: span 8 / span 8;
}

.col-span-9 {
  grid-column: span 9 / span 9;
}

.col-start-2 {
  grid-column-start: 2;
}

.row-span-1 {
  grid-row: span 1 / span 1;
}

.row-span-2 {
  grid-row: span 2 / span 2;
}

.row-span-3 {
  grid-row: span 3 / span 3;
}

.row-span-4 {
  grid-row: span 4 / span 4;
}

.float-right {
  float: right;
}

.-m-0\.5 {
  margin: -0.125rem;
}

.-m-2 {
  margin: -0.5rem;
}

.m-0 {
  margin: 0px;
}

.m-10 {
  margin: 2.5rem;
}

.m-2 {
  margin: 0.5rem;
}

.m-4 {
  margin: 1rem;
}

.-mx-2 {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}

.-mx-4 {
  margin-left: -1rem;
  margin-right: -1rem;
}

.-mx-px {
  margin-left: -1px;
  margin-right: -1px;
}

.-my-2 {
  margin-top: -0.5rem;
  margin-bottom: -0.5rem;
}

.-my-3 {
  margin-top: -0.75rem;
  margin-bottom: -0.75rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-24 {
  margin-top: 6rem;
  margin-bottom: 6rem;
}

.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.-mb-10 {
  margin-bottom: -2.5rem;
}

.-mb-12 {
  margin-bottom: -3rem;
}

.-mb-px {
  margin-bottom: -1px;
}

.-ml-12 {
  margin-left: -3rem;
}

.-ml-16 {
  margin-left: -4rem;
}

.-ml-px {
  margin-left: -1px;
}

.-mr-1 {
  margin-right: -0.25rem;
}

.-mr-2 {
  margin-right: -0.5rem;
}

.-mr-48 {
  margin-right: -12rem;
}

.-mr-96 {
  margin-right: -24rem;
}

.-mt-1\.5 {
  margin-top: -0.375rem;
}

.-mt-12 {
  margin-top: -3rem;
}

.-mt-4 {
  margin-top: -1rem;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-20 {
  margin-bottom: 5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-9 {
  margin-bottom: 2.25rem;
}

.ml-0\.5 {
  margin-left: 0.125rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-20 {
  margin-left: 5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

.ml-8 {
  margin-left: 2rem;
}

.ml-auto {
  margin-left: auto;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-1\.5 {
  margin-right: 0.375rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-6 {
  margin-right: 1.5rem;
}

.mr-auto {
  margin-right: auto;
}

.mt-0 {
  margin-top: 0px;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-14 {
  margin-top: 3.5rem;
}

.mt-16 {
  margin-top: 4rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-20 {
  margin-top: 5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-9 {
  margin-top: 2.25rem;
}

.mt-auto {
  margin-top: auto;
}

.box-content {
  box-sizing: content-box;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.line-clamp-4 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}

.line-clamp-5 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
}

.line-clamp-6 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 6;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.flow-root {
  display: flow-root;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.hidden {
  display: none;
}

.aspect-\[2\/3\] {
  aspect-ratio: 2/3;
}

.aspect-\[3\/2\] {
  aspect-ratio: 3/2;
}

.aspect-\[3\/4\] {
  aspect-ratio: 3/4;
}

.aspect-\[6\/5\] {
  aspect-ratio: 6/5;
}

.aspect-auto {
  aspect-ratio: auto;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-thumb {
  aspect-ratio: 4 / 3;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.h-1\/2 {
  height: 50%;
}

.h-1\/3 {
  height: 33.333333%;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-20 {
  height: 5rem;
}

.h-24 {
  height: 6rem;
}

.h-28 {
  height: 7rem;
}

.h-32 {
  height: 8rem;
}

.h-4 {
  height: 1rem;
}

.h-40 {
  height: 10rem;
}

.h-48 {
  height: 12rem;
}

.h-5 {
  height: 1.25rem;
}

.h-56 {
  height: 14rem;
}

.h-6 {
  height: 1.5rem;
}

.h-7 {
  height: 1.75rem;
}

.h-8 {
  height: 2rem;
}

.h-96 {
  height: 24rem;
}

.h-\[300px\] {
  height: 300px;
}

.h-\[320px\] {
  height: 320px;
}

.h-\[450px\] {
  height: 450px;
}

.h-\[500px\] {
  height: 500px;
}

.h-auto {
  height: auto;
}

.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

.max-h-12 {
  max-height: 3rem;
}

.max-h-16 {
  max-height: 4rem;
}

.max-h-28 {
  max-height: 7rem;
}

.max-h-32 {
  max-height: 8rem;
}

.max-h-72 {
  max-height: 18rem;
}

.max-h-96 {
  max-height: 24rem;
}

.max-h-\[320px\] {
  max-height: 320px;
}

.max-h-full {
  max-height: 100%;
}

.min-h-\[466px\] {
  min-height: 466px;
}

.min-h-full {
  min-height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

.w-0 {
  width: 0px;
}

.w-0\.5 {
  width: 0.125rem;
}

.w-1\/2 {
  width: 50%;
}

.w-1\/3 {
  width: 33.333333%;
}

.w-1\/5 {
  width: 20%;
}

.w-1\/6 {
  width: 16.666667%;
}

.w-10 {
  width: 2.5rem;
}

.w-10\/12 {
  width: 83.333333%;
}

.w-12 {
  width: 3rem;
}

.w-14 {
  width: 3.5rem;
}

.w-2\/3 {
  width: 66.666667%;
}

.w-2\/5 {
  width: 40%;
}

.w-20 {
  width: 5rem;
}

.w-28 {
  width: 7rem;
}

.w-3\/4 {
  width: 75%;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: 1rem;
}

.w-40 {
  width: 10rem;
}

.w-44 {
  width: 11rem;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-5\/6 {
  width: 83.333333%;
}

.w-56 {
  width: 14rem;
}

.w-6 {
  width: 1.5rem;
}

.w-8 {
  width: 2rem;
}

.w-\[200\%\] {
  width: 200%;
}

.w-\[40\%\] {
  width: 40%;
}

.w-\[48rem\] {
  width: 48rem;
}

.w-\[60\%\] {
  width: 60%;
}

.w-\[600px\] {
  width: 600px;
}

.w-\[68\.5625rem\] {
  width: 68.5625rem;
}

.w-\[98\%\] {
  width: 98%;
}

.w-auto {
  width: auto;
}

.w-full {
  width: 100%;
}

.w-px {
  width: 1px;
}

.w-screen {
  width: 100vw;
}

.min-w-0 {
  min-width: 0px;
}

.min-w-full {
  min-width: 100%;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-52 {
  max-width: 13rem;
}

.max-w-5xl {
  max-width: 64rem;
}

.max-w-6xl {
  max-width: 72rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-\[1440px\] {
  max-width: 1440px;
}

.max-w-full {
  max-width: 100%;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-min {
  max-width: -moz-min-content;
  max-width: min-content;
}

.max-w-none {
  max-width: none;
}

.max-w-screen-xl {
  max-width: 1280px;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xl {
  max-width: 36rem;
}

.max-w-xs {
  max-width: 20rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-none {
  flex: none;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink {
  flex-shrink: 1;
}

.shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.origin-top-right {
  transform-origin: top right;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-12 {
  --tw-translate-y: -3rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-16 {
  --tw-translate-y: -4rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-full {
  --tw-translate-y: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-24 {
  --tw-translate-x: 6rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-5 {
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-7 {
  --tw-translate-x: 1.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-2 {
  --tw-translate-y: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-1\/2 {
  --tw-translate-y: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-180 {
  --tw-rotate: -180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-90 {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.skew-x-\[-30deg\] {
  --tw-skew-x: -30deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-90 {
  --tw-scale-x: .9;
  --tw-scale-y: .9;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform-gpu {
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.cursor-default {
  cursor: default;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.resize {
  resize: both;
}

.scroll-py-2 {
  scroll-padding-top: 0.5rem;
  scroll-padding-bottom: 0.5rem;
}

.scroll-py-3 {
  scroll-padding-top: 0.75rem;
  scroll-padding-bottom: 0.75rem;
}

.list-inside {
  list-style-position: inside;
}

.list-\[circle\] {
  list-style-type: circle;
}

.list-disc {
  list-style-type: disc;
}

.auto-rows-max {
  grid-auto-rows: max-content;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.content-center {
  align-content: center;
}

.content-end {
  align-content: flex-end;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-0 {
  gap: 0px;
}

.gap-0\.5 {
  gap: 0.125rem;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-12 {
  gap: 3rem;
}

.gap-16 {
  gap: 4rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-20 {
  gap: 5rem;
}

.gap-24 {
  gap: 6rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-px {
  gap: 1px;
}

.gap-x-0 {
  -moz-column-gap: 0px;
       column-gap: 0px;
}

.gap-x-12 {
  -moz-column-gap: 3rem;
       column-gap: 3rem;
}

.gap-x-14 {
  -moz-column-gap: 3.5rem;
       column-gap: 3.5rem;
}

.gap-x-16 {
  -moz-column-gap: 4rem;
       column-gap: 4rem;
}

.gap-x-2 {
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}

.gap-x-3 {
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}

.gap-x-32 {
  -moz-column-gap: 8rem;
       column-gap: 8rem;
}

.gap-x-4 {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}

.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}

.gap-x-8 {
  -moz-column-gap: 2rem;
       column-gap: 2rem;
}

.gap-y-0 {
  row-gap: 0px;
}

.gap-y-10 {
  row-gap: 2.5rem;
}

.gap-y-12 {
  row-gap: 3rem;
}

.gap-y-16 {
  row-gap: 4rem;
}

.gap-y-2 {
  row-gap: 0.5rem;
}

.gap-y-3 {
  row-gap: 0.75rem;
}

.gap-y-32 {
  row-gap: 8rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.gap-y-6 {
  row-gap: 1.5rem;
}

.gap-y-8 {
  row-gap: 2rem;
}

.-space-x-px > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-1px * var(--tw-space-x-reverse));
  margin-left: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0px * var(--tw-space-x-reverse));
  margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(3rem * var(--tw-space-x-reverse));
  margin-left: calc(3rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));
}

.space-y-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3rem * var(--tw-space-y-reverse));
}

.space-y-14 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(3.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3.5rem * var(--tw-space-y-reverse));
}

.space-y-16 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(4rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-20 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-gray-100 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(245 245 244 / var(--tw-divide-opacity));
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(231 229 228 / var(--tw-divide-opacity));
}

.divide-gray-300 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(214 211 209 / var(--tw-divide-opacity));
}

.divide-gray-400 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(168 162 158 / var(--tw-divide-opacity));
}

.divide-gray-500 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(120 113 108 / var(--tw-divide-opacity));
}

.divide-secondary-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(168 255 128 / var(--tw-divide-opacity));
}

.justify-self-end {
  justify-self: end;
}

.justify-self-center {
  justify-self: center;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-visible {
  overflow: visible;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hyphens-auto {
  -webkit-hyphens: auto;
          hyphens: auto;
}

.whitespace-normal {
  white-space: normal;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.text-balance {
  text-wrap: balance;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-3xl {
  border-radius: 1.5rem;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-none {
  border-radius: 0px;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-b-lg {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.rounded-l-3xl {
  border-top-left-radius: 1.5rem;
  border-bottom-left-radius: 1.5rem;
}

.rounded-l-full {
  border-top-left-radius: 9999px;
  border-bottom-left-radius: 9999px;
}

.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-2 {
  border-width: 2px;
}

.border-4 {
  border-width: 4px;
}

.border-\[3px\] {
  border-width: 3px;
}

.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.border-y-2 {
  border-top-width: 2px;
  border-bottom-width: 2px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-r {
  border-right-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-dashed {
  border-style: dashed;
}

.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(231 229 228 / var(--tw-border-opacity));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(214 211 209 / var(--tw-border-opacity));
}

.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(168 162 158 / var(--tw-border-opacity));
}

.border-gray-500 {
  --tw-border-opacity: 1;
  border-color: rgb(120 113 108 / var(--tw-border-opacity));
}

.border-gray-800\/10 {
  border-color: rgb(41 37 36 / 0.1);
}

.border-gray-900\/10 {
  border-color: rgb(28 25 23 / 0.1);
}

.border-gray-900\/20 {
  border-color: rgb(28 25 23 / 0.2);
}

.border-gray-900\/25 {
  border-color: rgb(28 25 23 / 0.25);
}

.border-gray-900\/5 {
  border-color: rgb(28 25 23 / 0.05);
}

.border-green-300 {
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity));
}

.border-primary-300 {
  --tw-border-opacity: 1;
  border-color: rgb(248 203 168 / var(--tw-border-opacity));
}

.border-primary-500 {
  --tw-border-opacity: 1;
  border-color: rgb(243 155 78 / var(--tw-border-opacity));
}

.border-primary-500\/30 {
  border-color: rgb(243 155 78 / 0.3);
}

.border-primary-600 {
  --tw-border-opacity: 1;
  border-color: rgb(238 116 2 / var(--tw-border-opacity));
}

.border-primary-dark {
  --tw-border-opacity: 1;
  border-color: rgb(129 59 13 / var(--tw-border-opacity));
}

.border-red-100 {
  --tw-border-opacity: 1;
  border-color: rgb(254 226 226 / var(--tw-border-opacity));
}

.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
}

.border-secondary-300 {
  --tw-border-opacity: 1;
  border-color: rgb(110 255 55 / var(--tw-border-opacity));
}

.border-secondary-500\/20 {
  border-color: rgb(27 255 0 / 0.2);
}

.border-transparent {
  border-color: transparent;
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.border-zinc-200 {
  --tw-border-opacity: 1;
  border-color: rgb(228 228 231 / var(--tw-border-opacity));
}

.border-b-blue-700 {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(29 78 216 / var(--tw-border-opacity));
}

.border-opacity-10 {
  --tw-border-opacity: 0.1;
}

.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity));
}

.bg-amber-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-black\/10 {
  background-color: rgb(0 0 0 / 0.1);
}

.bg-black\/20 {
  background-color: rgb(0 0 0 / 0.2);
}

.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 244 / var(--tw-bg-opacity));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(231 229 228 / var(--tw-bg-opacity));
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(214 211 209 / var(--tw-bg-opacity));
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 162 158 / var(--tw-bg-opacity));
}

.bg-gray-400\/20 {
  background-color: rgb(168 162 158 / 0.2);
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 249 / var(--tw-bg-opacity));
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(120 113 108 / var(--tw-bg-opacity));
}

.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(87 83 78 / var(--tw-bg-opacity));
}

.bg-gray-600\/50 {
  background-color: rgb(87 83 78 / 0.5);
}

.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(68 64 60 / var(--tw-bg-opacity));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(41 37 36 / var(--tw-bg-opacity));
}

.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(28 25 23 / var(--tw-bg-opacity));
}

.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}

.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity));
}

.bg-green-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity));
}

.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity));
}

.bg-lime-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(132 204 22 / var(--tw-bg-opacity));
}

.bg-lime-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(101 163 13 / var(--tw-bg-opacity));
}

.bg-neutral-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(38 38 38 / var(--tw-bg-opacity));
}

.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity));
}

.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity));
}

.bg-orange-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity));
}

.bg-pink-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 72 153 / var(--tw-bg-opacity));
}

.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(238 116 2 / var(--tw-bg-opacity));
}

.bg-primary-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 233 224 / var(--tw-bg-opacity));
}

.bg-primary-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 219 201 / var(--tw-bg-opacity));
}

.bg-primary-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 243 240 / var(--tw-bg-opacity));
}

.bg-primary-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 155 78 / var(--tw-bg-opacity));
}

.bg-primary-500\/70 {
  background-color: rgb(243 155 78 / 0.7);
}

.bg-primary-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(238 116 2 / var(--tw-bg-opacity));
}

.bg-primary-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(167 75 2 / var(--tw-bg-opacity));
}

.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity));
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity));
}

.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}

.bg-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(8 99 0 / var(--tw-bg-opacity));
}

.bg-secondary-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(210 255 187 / var(--tw-bg-opacity));
}

.bg-secondary-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(27 255 0 / var(--tw-bg-opacity));
}

.bg-secondary-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(12 211 0 / var(--tw-bg-opacity));
}

.bg-sky-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity));
}

.bg-sky-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 132 199 / var(--tw-bg-opacity));
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-white\/90 {
  background-color: rgb(255 255 255 / 0.9);
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}

.bg-zinc-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 245 / var(--tw-bg-opacity));
}

.bg-opacity-25 {
  --tw-bg-opacity: 0.25;
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-l {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-\[\#002E50\]\/90 {
  --tw-gradient-from: rgb(0 46 80 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 46 80 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black\/60 {
  --tw-gradient-from: rgb(0 0 0 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black\/80 {
  --tw-gradient-from: rgb(0 0 0 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-800 {
  --tw-gradient-from: #292524 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(41 37 36 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary-100\/30 {
  --tw-gradient-from: rgb(251 233 224 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(251 233 224 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary-600 {
  --tw-gradient-from: #EE7402 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(238 116 2 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white {
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-black\/40 {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-black\/70 {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.7) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-primary-500 {
  --tw-gradient-to: rgb(243 155 78 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #F39B4E var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white\/80 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white\/90 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.to-\[\#002E50\]\/80 {
  --tw-gradient-to: rgb(0 46 80 / 0.8) var(--tw-gradient-to-position);
}

.to-black\/60 {
  --tw-gradient-to: rgb(0 0 0 / 0.6) var(--tw-gradient-to-position);
}

.to-primary-600 {
  --tw-gradient-to: #EE7402 var(--tw-gradient-to-position);
}

.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.stroke-white\/20 {
  stroke: rgb(255 255 255 / 0.2);
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.object-bottom {
  -o-object-position: bottom;
     object-position: bottom;
}

.object-center {
  -o-object-position: center;
     object-position: center;
}

.object-left {
  -o-object-position: left;
     object-position: left;
}

.object-left-top {
  -o-object-position: left top;
     object-position: left top;
}

.object-right {
  -o-object-position: right;
     object-position: right;
}

.object-top {
  -o-object-position: top;
     object-position: top;
}

.p-0 {
  padding: 0px;
}

.p-0\.5 {
  padding: 0.125rem;
}

.p-1 {
  padding: 0.25rem;
}

.p-10 {
  padding: 2.5rem;
}

.p-12 {
  padding: 3rem;
}

.p-16 {
  padding: 4rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.p-px {
  padding: 1px;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-14 {
  padding-left: 3.5rem;
  padding-right: 3.5rem;
}

.px-16 {
  padding-left: 4rem;
  padding-right: 4rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-14 {
  padding-top: 3.5rem;
  padding-bottom: 3.5rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.py-24 {
  padding-top: 6rem;
  padding-bottom: 6rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.pb-0\.5 {
  padding-bottom: 0.125rem;
}

.pb-12 {
  padding-bottom: 3rem;
}

.pb-16 {
  padding-bottom: 4rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-20 {
  padding-bottom: 5rem;
}

.pb-24 {
  padding-bottom: 6rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pb-8 {
  padding-bottom: 2rem;
}

.pb-9 {
  padding-bottom: 2.25rem;
}

.pl-0 {
  padding-left: 0px;
}

.pl-11 {
  padding-left: 2.75rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pr-1 {
  padding-right: 0.25rem;
}

.pr-12 {
  padding-right: 3rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

.pr-8 {
  padding-right: 2rem;
}

.pt-1 {
  padding-top: 0.25rem;
}

.pt-1\.5 {
  padding-top: 0.375rem;
}

.pt-10 {
  padding-top: 2.5rem;
}

.pt-12 {
  padding-top: 3rem;
}

.pt-16 {
  padding-top: 4rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-2\.5 {
  padding-top: 0.625rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-3\.5 {
  padding-top: 0.875rem;
}

.pt-32 {
  padding-top: 8rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-56 {
  padding-top: 14rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-64 {
  padding-top: 16rem;
}

.pt-8 {
  padding-top: 2rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.align-top {
  vertical-align: top;
}

.align-middle {
  vertical-align: middle;
}

.align-bottom {
  vertical-align: bottom;
}

.font-sans {
  font-family: Open Sans, ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

.text-7xl {
  font-size: 4.5rem;
  line-height: 1;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-xxs {
  font-size: 11px;
  line-height: 14px;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.not-italic {
  font-style: normal;
}

.leading-10 {
  line-height: 2.5rem;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-7 {
  line-height: 1.75rem;
}

.leading-8 {
  line-height: 2rem;
}

.leading-none {
  line-height: 1;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-snug {
  line-height: 1.375;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}

.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(231 229 228 / var(--tw-text-opacity));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(214 211 209 / var(--tw-text-opacity));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(168 162 158 / var(--tw-text-opacity));
}

.text-gray-50 {
  --tw-text-opacity: 1;
  color: rgb(250 250 249 / var(--tw-text-opacity));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(120 113 108 / var(--tw-text-opacity));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(87 83 78 / var(--tw-text-opacity));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(68 64 60 / var(--tw-text-opacity));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(41 37 36 / var(--tw-text-opacity));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(28 25 23 / var(--tw-text-opacity));
}

.text-green-200 {
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity));
}

.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity));
}

.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity));
}

.text-indigo-200 {
  --tw-text-opacity: 1;
  color: rgb(199 210 254 / var(--tw-text-opacity));
}

.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity));
}

.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity));
}

.text-orange-700 {
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity));
}

.text-primary {
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

.text-primary-100 {
  --tw-text-opacity: 1;
  color: rgb(251 233 224 / var(--tw-text-opacity));
}

.text-primary-200 {
  --tw-text-opacity: 1;
  color: rgb(250 219 201 / var(--tw-text-opacity));
}

.text-primary-300 {
  --tw-text-opacity: 1;
  color: rgb(248 203 168 / var(--tw-text-opacity));
}

.text-primary-50 {
  --tw-text-opacity: 1;
  color: rgb(248 243 240 / var(--tw-text-opacity));
}

.text-primary-500 {
  --tw-text-opacity: 1;
  color: rgb(243 155 78 / var(--tw-text-opacity));
}

.text-primary-600 {
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

.text-primary-700 {
  --tw-text-opacity: 1;
  color: rgb(204 95 2 / var(--tw-text-opacity));
}

.text-primary-900 {
  --tw-text-opacity: 1;
  color: rgb(127 55 1 / var(--tw-text-opacity));
}

.text-primary-hover {
  --tw-text-opacity: 1;
  color: rgb(204 95 2 / var(--tw-text-opacity));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}

.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity));
}

.text-secondary-300 {
  --tw-text-opacity: 1;
  color: rgb(110 255 55 / var(--tw-text-opacity));
}

.text-secondary-400 {
  --tw-text-opacity: 1;
  color: rgb(60 255 0 / var(--tw-text-opacity));
}

.text-secondary-500 {
  --tw-text-opacity: 1;
  color: rgb(27 255 0 / var(--tw-text-opacity));
}

.text-secondary-700 {
  --tw-text-opacity: 1;
  color: rgb(9 159 0 / var(--tw-text-opacity));
}

.text-secondary-800 {
  --tw-text-opacity: 1;
  color: rgb(10 123 0 / var(--tw-text-opacity));
}

.text-secondary-900 {
  --tw-text-opacity: 1;
  color: rgb(8 99 0 / var(--tw-text-opacity));
}

.text-sky-500 {
  --tw-text-opacity: 1;
  color: rgb(14 165 233 / var(--tw-text-opacity));
}

.text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}

.text-slate-600 {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.text-social-facebook {
  --tw-text-opacity: 1;
  color: rgb(59 89 153 / var(--tw-text-opacity));
}

.text-social-github {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-social-instagram {
  --tw-text-opacity: 1;
  color: rgb(228 64 95 / var(--tw-text-opacity));
}

.text-social-linkedin {
  --tw-text-opacity: 1;
  color: rgb(0 119 181 / var(--tw-text-opacity));
}

.text-social-snapchat {
  --tw-text-opacity: 1;
  color: rgb(255 252 0 / var(--tw-text-opacity));
}

.text-social-tiktok {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-social-twitter {
  --tw-text-opacity: 1;
  color: rgb(85 172 238 / var(--tw-text-opacity));
}

.text-social-whatsapp {
  --tw-text-opacity: 1;
  color: rgb(37 211 102 / var(--tw-text-opacity));
}

.text-social-youtube {
  --tw-text-opacity: 1;
  color: rgb(205 32 31 / var(--tw-text-opacity));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-zinc-500 {
  --tw-text-opacity: 1;
  color: rgb(113 113 122 / var(--tw-text-opacity));
}

.text-zinc-700 {
  --tw-text-opacity: 1;
  color: rgb(63 63 70 / var(--tw-text-opacity));
}

.text-zinc-900 {
  --tw-text-opacity: 1;
  color: rgb(24 24 27 / var(--tw-text-opacity));
}

.underline {
  text-decoration-line: underline;
}

.line-through {
  text-decoration-line: line-through;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.placeholder-gray-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(168 162 158 / var(--tw-placeholder-opacity));
}

.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(168 162 158 / var(--tw-placeholder-opacity));
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-60 {
  opacity: 0.6;
}

.opacity-70 {
  opacity: 0.7;
}

.opacity-80 {
  opacity: 0.8;
}

.mix-blend-multiply {
  mix-blend-mode: multiply;
}

.mix-blend-soft-light {
  mix-blend-mode: soft-light;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-black\/20 {
  --tw-shadow-color: rgb(0 0 0 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-black\/30 {
  --tw-shadow-color: rgb(0 0 0 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-black\/40 {
  --tw-shadow-color: rgb(0 0 0 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-primary-500\/30 {
  --tw-shadow-color: rgb(243 155 78 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline {
  outline-style: solid;
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-inset {
  --tw-ring-inset: inset;
}

.ring-black {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity));
}

.ring-blue-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));
}

.ring-gray-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(214 211 209 / var(--tw-ring-opacity));
}

.ring-gray-400\/10 {
  --tw-ring-color: rgb(168 162 158 / 0.1);
}

.ring-gray-900\/5 {
  --tw-ring-color: rgb(28 25 23 / 0.05);
}

.ring-green-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity));
}

.ring-indigo-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(238 242 255 / var(--tw-ring-opacity));
}

.ring-orange-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity));
}

.ring-pink-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(236 72 153 / var(--tw-ring-opacity));
}

.ring-purple-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity));
}

.ring-yellow-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity));
}

.ring-opacity-5 {
  --tw-ring-opacity: 0.05;
}

.ring-offset-1 {
  --tw-ring-offset-width: 1px;
}

.ring-offset-2 {
  --tw-ring-offset-width: 2px;
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.brightness-125 {
  --tw-brightness: brightness(1.25);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.brightness-75 {
  --tw-brightness: brightness(.75);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.saturate-0 {
  --tw-saturate: saturate(0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-100 {
  transition-duration: 100ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.duration-75 {
  transition-duration: 75ms;
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-linear {
  transition-timing-function: linear;
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

@media (min-width: 768px) {
  .md\:container {
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    padding-right: 1rem;
    padding-left: 1rem;
  }

  @media (min-width: 425px) {
    .md\:container {
      max-width: 425px;
    }
  }

  @media (min-width: 640px) {
    .md\:container {
      max-width: 640px;
    }
  }

  @media (min-width: 768px) {
    .md\:container {
      max-width: 768px;
    }
  }

  @media (min-width: 1024px) {
    .md\:container {
      max-width: 1024px;
    }
  }

  @media (min-width: 1280px) {
    .md\:container {
      max-width: 1280px;
    }
  }

  @media (min-width: 1536px) {
    .md\:container {
      max-width: 1536px;
    }
  }

  .md\:aspect-h-3 {
    --tw-aspect-h: 3;
  }

  .md\:aspect-h-9 {
    --tw-aspect-h: 9;
  }

  .md\:aspect-w-16 {
    position: relative;
    padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
    --tw-aspect-w: 16;
  }

  .md\:aspect-w-16 > * {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  .md\:aspect-w-4 {
    position: relative;
    padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
    --tw-aspect-w: 4;
  }

  .md\:aspect-w-4 > * {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}

@media (min-width: 1024px) {
  .lg\:container {
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    padding-right: 1rem;
    padding-left: 1rem;
  }

  @media (min-width: 425px) {
    .lg\:container {
      max-width: 425px;
    }
  }

  @media (min-width: 640px) {
    .lg\:container {
      max-width: 640px;
    }
  }

  @media (min-width: 768px) {
    .lg\:container {
      max-width: 768px;
    }
  }

  @media (min-width: 1024px) {
    .lg\:container {
      max-width: 1024px;
    }
  }

  @media (min-width: 1280px) {
    .lg\:container {
      max-width: 1280px;
    }
  }

  @media (min-width: 1536px) {
    .lg\:container {
      max-width: 1536px;
    }
  }

  .lg\:prose-lg {
    font-size: 1.125rem;
    line-height: 1.7777778;
  }

  .lg\:prose-lg :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
  }

  .lg\:prose-lg :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.2222222em;
    line-height: 1.4545455;
    margin-top: 1.0909091em;
    margin-bottom: 1.0909091em;
  }

  .lg\:prose-lg :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.6666667em;
    margin-bottom: 1.6666667em;
    padding-inline-start: 1em;
  }

  .lg\:prose-lg :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 2.6666667em;
    margin-top: 0;
    margin-bottom: 0.8333333em;
    line-height: 1;
  }

  .lg\:prose-lg :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.6666667em;
    margin-top: 1.8666667em;
    margin-bottom: 1.0666667em;
    line-height: 1.3333333;
  }

  .lg\:prose-lg :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.3333333em;
    margin-top: 1.6666667em;
    margin-bottom: 0.6666667em;
    line-height: 1.5;
  }

  .lg\:prose-lg :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 0.4444444em;
    line-height: 1.5555556;
  }

  .lg\:prose-lg :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .lg\:prose-lg :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .lg\:prose-lg :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .lg\:prose-lg :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .lg\:prose-lg :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    border-radius: 0.3125rem;
    padding-top: 0.2222222em;
    padding-inline-end: 0.4444444em;
    padding-bottom: 0.2222222em;
    padding-inline-start: 0.4444444em;
  }

  .lg\:prose-lg :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
  }

  .lg\:prose-lg :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8666667em;
  }

  .lg\:prose-lg :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.875em;
  }

  .lg\:prose-lg :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.75;
    margin-top: 2em;
    margin-bottom: 2em;
    border-radius: 0.375rem;
    padding-top: 1em;
    padding-inline-end: 1.5em;
    padding-bottom: 1em;
    padding-inline-start: 1.5em;
  }

  .lg\:prose-lg :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
    padding-inline-start: 1.5555556em;
  }

  .lg\:prose-lg :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
    padding-inline-start: 1.5555556em;
  }

  .lg\:prose-lg :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.6666667em;
    margin-bottom: 0.6666667em;
  }

  .lg\:prose-lg :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0.4444444em;
  }

  .lg\:prose-lg :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0.4444444em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.8888889em;
    margin-bottom: 0.8888889em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 1.3333333em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 1.3333333em;
  }

  .lg\:prose-lg :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.8888889em;
    margin-bottom: 0.8888889em;
  }

  .lg\:prose-lg :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
  }

  .lg\:prose-lg :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .lg\:prose-lg :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.6666667em;
    padding-inline-start: 1.5555556em;
  }

  .lg\:prose-lg :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 3.1111111em;
    margin-bottom: 3.1111111em;
  }

  .lg\:prose-lg :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.5;
  }

  .lg\:prose-lg :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0.75em;
    padding-bottom: 0.75em;
    padding-inline-start: 0.75em;
  }

  .lg\:prose-lg :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .lg\:prose-lg :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .lg\:prose-lg :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-top: 0.75em;
    padding-inline-end: 0.75em;
    padding-bottom: 0.75em;
    padding-inline-start: 0.75em;
  }

  .lg\:prose-lg :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .lg\:prose-lg :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .lg\:prose-lg :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .lg\:prose-lg :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .lg\:prose-lg :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.5;
    margin-top: 1em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 0;
  }
}

.dark\:prose-primary_inverted:where(.dark, .dark *) {
  --tw-prose-body: #f5f5f4;
  --tw-prose-headings: #f5f5f4;
  --tw-prose-lead: #fff;
  --tw-prose-links: #fff;
  --tw-prose-bold: #fff;
  --tw-prose-counters: #fff;
  --tw-prose-bullets: #fff;
  --tw-prose-hr: #CC5F02;
  --tw-prose-quotes: #fff;
  --tw-prose-quote-borders: #EE7402;
  --tw-prose-captions: #a8a29e;
  --tw-prose-code: #fff;
  --tw-prose-pre-code: #F8CBA8;
  --tw-prose-pre-bg: rgb(0 0 0 / 25%);
  --tw-prose-th-borders: #e7e5e4;
  --tw-prose-td-borders: #e7e5e4;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h1 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h2 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h3 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h4 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #EE7402;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #f5f5f4;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #f5f5f4;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #FBE9E0;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #FBE9E0;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)):hover {
  color: #EE7402;
}

.placeholder\:text-gray-400::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(168 162 158 / var(--tw-text-opacity));
}

.placeholder\:text-gray-400::placeholder {
  --tw-text-opacity: 1;
  color: rgb(168 162 158 / var(--tw-text-opacity));
}

.before\:mr-1::before {
  content: var(--tw-content);
  margin-right: 0.25rem;
}

.before\:content-\[\'\20AC\'\]::before {
  --tw-content: '€';
  content: var(--tw-content);
}

.after\:hidden::after {
  content: var(--tw-content);
  display: none;
}

.last-of-type\:border-b-0:last-of-type {
  border-bottom-width: 0px;
}

.focus-within\:outline-none:focus-within {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-within\:ring-2:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-within\:ring-primary-600:focus-within {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(238 116 2 / var(--tw-ring-opacity));
}

.focus-within\:ring-offset-2:focus-within {
  --tw-ring-offset-width: 2px;
}

.hover\:border-2:hover {
  border-width: 2px;
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(214 211 209 / var(--tw-border-opacity));
}

.hover\:border-gray-900\/50:hover {
  border-color: rgb(28 25 23 / 0.5);
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 244 / var(--tw-bg-opacity));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(231 229 228 / var(--tw-bg-opacity));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 249 / var(--tw-bg-opacity));
}

.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(87 83 78 / var(--tw-bg-opacity));
}

.hover\:bg-green-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity));
}

.hover\:bg-green-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}

.hover\:bg-indigo-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(67 56 202 / var(--tw-bg-opacity));
}

.hover\:bg-orange-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 215 170 / var(--tw-bg-opacity));
}

.hover\:bg-orange-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(194 65 12 / var(--tw-bg-opacity));
}

.hover\:bg-primary-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 219 201 / var(--tw-bg-opacity));
}

.hover\:bg-primary-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(248 203 168 / var(--tw-bg-opacity));
}

.hover\:bg-primary-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(238 116 2 / var(--tw-bg-opacity));
}

.hover\:bg-primary-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(204 95 2 / var(--tw-bg-opacity));
}

.hover\:bg-secondary-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(168 255 128 / var(--tw-bg-opacity));
}

.hover\:bg-secondary-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(12 211 0 / var(--tw-bg-opacity));
}

.hover\:bg-sky-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(2 132 199 / var(--tw-bg-opacity));
}

.hover\:text-gray-500:hover {
  --tw-text-opacity: 1;
  color: rgb(120 113 108 / var(--tw-text-opacity));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(87 83 78 / var(--tw-text-opacity));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(68 64 60 / var(--tw-text-opacity));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(41 37 36 / var(--tw-text-opacity));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(28 25 23 / var(--tw-text-opacity));
}

.hover\:text-orange-500:hover {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity));
}

.hover\:text-primary:hover {
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

.hover\:text-primary-200:hover {
  --tw-text-opacity: 1;
  color: rgb(250 219 201 / var(--tw-text-opacity));
}

.hover\:text-primary-400:hover {
  --tw-text-opacity: 1;
  color: rgb(245 180 125 / var(--tw-text-opacity));
}

.hover\:text-primary-500:hover {
  --tw-text-opacity: 1;
  color: rgb(243 155 78 / var(--tw-text-opacity));
}

.hover\:text-primary-600:hover {
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

.hover\:text-primary-700:hover {
  --tw-text-opacity: 1;
  color: rgb(204 95 2 / var(--tw-text-opacity));
}

.hover\:text-secondary-500:hover {
  --tw-text-opacity: 1;
  color: rgb(27 255 0 / var(--tw-text-opacity));
}

.hover\:text-secondary-600:hover {
  --tw-text-opacity: 1;
  color: rgb(12 211 0 / var(--tw-text-opacity));
}

.hover\:text-sky-600:hover {
  --tw-text-opacity: 1;
  color: rgb(2 132 199 / var(--tw-text-opacity));
}

.hover\:text-social-facebook:hover {
  --tw-text-opacity: 1;
  color: rgb(59 89 153 / var(--tw-text-opacity));
}

.hover\:text-social-github:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.hover\:text-social-instagram:hover {
  --tw-text-opacity: 1;
  color: rgb(228 64 95 / var(--tw-text-opacity));
}

.hover\:text-social-linkedin:hover {
  --tw-text-opacity: 1;
  color: rgb(0 119 181 / var(--tw-text-opacity));
}

.hover\:text-social-snapchat:hover {
  --tw-text-opacity: 1;
  color: rgb(255 252 0 / var(--tw-text-opacity));
}

.hover\:text-social-tiktok:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.hover\:text-social-x:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.hover\:text-social-youtube:hover {
  --tw-text-opacity: 1;
  color: rgb(205 32 31 / var(--tw-text-opacity));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-black\/40:hover {
  --tw-shadow-color: rgb(0 0 0 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:grayscale-0:hover {
  --tw-grayscale: grayscale(0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.focus\:z-20:focus {
  z-index: 20;
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:outline-offset-0:focus {
  outline-offset: 0px;
}

.focus\:ring:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-gray-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(120 113 108 / var(--tw-ring-opacity));
}

.focus\:ring-orange-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity));
}

.focus\:ring-primary-400:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(245 180 125 / var(--tw-ring-opacity));
}

.focus\:ring-primary-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(243 155 78 / var(--tw-ring-opacity));
}

.focus\:ring-primary-600:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(238 116 2 / var(--tw-ring-opacity));
}

.focus\:ring-secondary-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(27 255 0 / var(--tw-ring-opacity));
}

.focus\:ring-white:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus\:ring-offset-gray-50:focus {
  --tw-ring-offset-color: #fafaf9;
}

.focus-visible\:outline:focus-visible {
  outline-style: solid;
}

.focus-visible\:outline-2:focus-visible {
  outline-width: 2px;
}

.focus-visible\:outline-offset-2:focus-visible {
  outline-offset: 2px;
}

.focus-visible\:outline-offset-\[-4px\]:focus-visible {
  outline-offset: -4px;
}

.focus-visible\:outline-indigo-600:focus-visible {
  outline-color: #4f46e5;
}

.disabled\:bg-gray-400:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(168 162 158 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:block {
  display: block;
}

.group\/menuitem:hover .group-hover\/menuitem\:flex {
  display: flex;
}

.group\/card:hover .group-hover\/card\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-\[1\.15\] {
  --tw-scale-x: 1.15;
  --tw-scale-y: 1.15;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes ring {
  0%, 100% {
    transform: rotate(-6deg);
  }

  50% {
    transform: rotate(6deg);
  }
}

.group:hover .group-hover\:animate-ring {
  animation: ring 1s ease-in-out infinite;
}

.group\/card:hover .group-hover\/card\:bg-primary-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 243 240 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(238 116 2 / var(--tw-bg-opacity));
}

.group\/card:hover .group-hover\/card\:text-primary {
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(120 113 108 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary {
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary-400 {
  --tw-text-opacity: 1;
  color: rgb(245 180 125 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary-500 {
  --tw-text-opacity: 1;
  color: rgb(243 155 78 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary-600 {
  --tw-text-opacity: 1;
  color: rgb(238 116 2 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary-700 {
  --tw-text-opacity: 1;
  color: rgb(204 95 2 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary-800 {
  --tw-text-opacity: 1;
  color: rgb(167 75 2 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.group:hover .group-hover\:opacity-75 {
  opacity: 0.75;
}

.group:hover .group-hover\:opacity-80 {
  opacity: 0.8;
}

.peer:checked ~ .peer-checked\:bg-primary-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 155 78 / var(--tw-bg-opacity));
}

.peer:checked ~ .peer-checked\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.peer:hover ~ .peer-hover\:grayscale-0 {
  --tw-grayscale: grayscale(0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.prose-h3\:text-gray-700 :is(:where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgb(68 64 60 / var(--tw-text-opacity));
}

.prose-p\:text-gray-500 :is(:where(p):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgb(120 113 108 / var(--tw-text-opacity));
}

.prose-p\:text-gray-600 :is(:where(p):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgb(87 83 78 / var(--tw-text-opacity));
}

.prose-p\:text-gray-900 :is(:where(p):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgb(28 25 23 / var(--tw-text-opacity));
}

.prose-a\:no-underline :is(:where(a):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  text-decoration-line: none;
}

.hover\:prose-a\:underline :is(:where(a):not(:where([class~="not-prose"],[class~="not-prose"] *))):hover {
  text-decoration-line: underline;
}

.prose-ul\:pl-2 :is(:where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  padding-left: 0.5rem;
}

@media (min-width: 425px) {
  .xs\:ml-0 {
    margin-left: 0px;
  }

  .xs\:block {
    display: block;
  }
}

@media (min-width: 640px) {
  .sm\:order-2 {
    order: 2;
  }

  .sm\:order-3 {
    order: 3;
  }

  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .sm\:-mx-6 {
    margin-left: -1.5rem;
    margin-right: -1.5rem;
  }

  .sm\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .sm\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .sm\:-mr-80 {
    margin-right: -20rem;
  }

  .sm\:ml-2 {
    margin-left: 0.5rem;
  }

  .sm\:ml-4 {
    margin-left: 1rem;
  }

  .sm\:ml-6 {
    margin-left: 1.5rem;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:mt-16 {
    margin-top: 4rem;
  }

  .sm\:mt-20 {
    margin-top: 5rem;
  }

  .sm\:mt-4 {
    margin-top: 1rem;
  }

  .sm\:mt-5 {
    margin-top: 1.25rem;
  }

  .sm\:mt-6 {
    margin-top: 1.5rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline-block {
    display: inline-block;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:grid {
    display: grid;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:aspect-auto {
    aspect-ratio: auto;
  }

  .sm\:h-10 {
    height: 2.5rem;
  }

  .sm\:h-2\/3 {
    height: 66.666667%;
  }

  .sm\:h-32 {
    height: 8rem;
  }

  .sm\:h-72 {
    height: 18rem;
  }

  .sm\:h-full {
    height: 100%;
  }

  .sm\:h-screen {
    height: 100vh;
  }

  .sm\:w-10 {
    width: 2.5rem;
  }

  .sm\:w-\[57rem\] {
    width: 57rem;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:min-w-0 {
    min-width: 0px;
  }

  .sm\:max-w-3xl {
    max-width: 48rem;
  }

  .sm\:max-w-4xl {
    max-width: 56rem;
  }

  .sm\:max-w-lg {
    max-width: 32rem;
  }

  .sm\:max-w-xl {
    max-width: 36rem;
  }

  .sm\:flex-1 {
    flex: 1 1 0%;
  }

  .sm\:shrink-0 {
    flex-shrink: 0;
  }

  .sm\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .sm\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-col {
    flex-direction: column;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-center {
    justify-content: center;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-8 {
    gap: 2rem;
  }

  .sm\:gap-x-6 {
    -moz-column-gap: 1.5rem;
         column-gap: 1.5rem;
  }

  .sm\:gap-y-10 {
    row-gap: 2.5rem;
  }

  .sm\:gap-y-20 {
    row-gap: 5rem;
  }

  .sm\:gap-y-24 {
    row-gap: 6rem;
  }

  .sm\:rounded-lg {
    border-radius: 0.5rem;
  }

  .sm\:rounded-md {
    border-radius: 0.375rem;
  }

  .sm\:p-0 {
    padding: 0px;
  }

  .sm\:p-10 {
    padding: 2.5rem;
  }

  .sm\:p-3 {
    padding: 0.75rem;
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }

  .sm\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .sm\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .sm\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .sm\:px-14 {
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }

  .sm\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .sm\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .sm\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .sm\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .sm\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .sm\:pb-12 {
    padding-bottom: 3rem;
  }

  .sm\:pb-32 {
    padding-bottom: 8rem;
  }

  .sm\:pb-5 {
    padding-bottom: 1.25rem;
  }

  .sm\:pb-6 {
    padding-bottom: 1.5rem;
  }

  .sm\:pl-0 {
    padding-left: 0px;
  }

  .sm\:pl-6 {
    padding-left: 1.5rem;
  }

  .sm\:pr-0 {
    padding-right: 0px;
  }

  .sm\:pr-6 {
    padding-right: 1.5rem;
  }

  .sm\:pt-12 {
    padding-top: 3rem;
  }

  .sm\:pt-16 {
    padding-top: 4rem;
  }

  .sm\:pt-24 {
    padding-top: 6rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-center {
    text-align: center;
  }

  .sm\:align-middle {
    vertical-align: middle;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .sm\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:tracking-tight {
    letter-spacing: -0.025em;
  }
}

@media (min-width: 768px) {
  .md\:absolute {
    position: absolute;
  }

  .md\:relative {
    position: relative;
  }

  .md\:inset-0 {
    inset: 0px;
  }

  .md\:inset-y-0 {
    top: 0px;
    bottom: 0px;
  }

  .md\:inset-x-0 {
    left: 0px;
    right: 0px;
  }

  .md\:-bottom-8 {
    bottom: -2rem;
  }

  .md\:bottom-10 {
    bottom: 2.5rem;
  }

  .md\:bottom-auto {
    bottom: auto;
  }

  .md\:left-0 {
    left: 0px;
  }

  .md\:left-auto {
    left: auto;
  }

  .md\:right-0 {
    right: 0px;
  }

  .md\:right-1\/2 {
    right: 50%;
  }

  .md\:top-1\/2 {
    top: 50%;
  }

  .md\:top-0 {
    top: 0px;
  }

  .md\:bottom-0 {
    bottom: 0px;
  }

  .md\:z-20 {
    z-index: 20;
  }

  .md\:order-1 {
    order: 1;
  }

  .md\:order-2 {
    order: 2;
  }

  .md\:order-3 {
    order: 3;
  }

  .md\:order-4 {
    order: 4;
  }

  .md\:order-first {
    order: -9999;
  }

  .md\:order-last {
    order: 9999;
  }

  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .md\:col-span-10 {
    grid-column: span 10 / span 10;
  }

  .md\:col-span-11 {
    grid-column: span 11 / span 11;
  }

  .md\:col-span-12 {
    grid-column: span 12 / span 12;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .md\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .md\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .md\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .md\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .md\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .md\:col-start-2 {
    grid-column-start: 2;
  }

  .md\:col-start-3 {
    grid-column-start: 3;
  }

  .md\:col-start-6 {
    grid-column-start: 6;
  }

  .md\:row-span-1 {
    grid-row: span 1 / span 1;
  }

  .md\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .md\:row-span-3 {
    grid-row: span 3 / span 3;
  }

  .md\:row-span-4 {
    grid-row: span 4 / span 4;
  }

  .md\:-mx-4 {
    margin-left: -1rem;
    margin-right: -1rem;
  }

  .md\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .md\:mx-20 {
    margin-left: 5rem;
    margin-right: 5rem;
  }

  .md\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .md\:my-3 {
    margin-top: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .md\:my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .md\:-mb-12 {
    margin-bottom: -3rem;
  }

  .md\:-mb-16 {
    margin-bottom: -4rem;
  }

  .md\:-ml-4 {
    margin-left: -1rem;
  }

  .md\:-mr-16 {
    margin-right: -4rem;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-12 {
    margin-bottom: 3rem;
  }

  .md\:mb-3 {
    margin-bottom: 0.75rem;
  }

  .md\:ml-auto {
    margin-left: auto;
  }

  .md\:mr-0 {
    margin-right: 0px;
  }

  .md\:mr-2 {
    margin-right: 0.5rem;
  }

  .md\:mr-3 {
    margin-right: 0.75rem;
  }

  .md\:mr-auto {
    margin-right: auto;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-10 {
    margin-top: 2.5rem;
  }

  .md\:mt-12 {
    margin-top: 3rem;
  }

  .md\:mt-14 {
    margin-top: 3.5rem;
  }

  .md\:mt-16 {
    margin-top: 4rem;
  }

  .md\:mt-32 {
    margin-top: 8rem;
  }

  .md\:mt-4 {
    margin-top: 1rem;
  }

  .md\:mt-5 {
    margin-top: 1.25rem;
  }

  .md\:block {
    display: block;
  }

  .md\:inline-block {
    display: inline-block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:inline-flex {
    display: inline-flex;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:aspect-auto {
    aspect-ratio: auto;
  }

  .md\:aspect-square {
    aspect-ratio: 1 / 1;
  }

  .md\:aspect-thumb {
    aspect-ratio: 4 / 3;
  }

  .md\:aspect-video {
    aspect-ratio: 16 / 9;
  }

  .md\:h-96 {
    height: 24rem;
  }

  .md\:h-\[450px\] {
    height: 450px;
  }

  .md\:h-\[600px\] {
    height: 600px;
  }

  .md\:h-full {
    height: 100%;
  }

  .md\:h-px {
    height: 1px;
  }

  .md\:max-h-\[440px\] {
    max-height: 440px;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-1\/3 {
    width: 33.333333%;
  }

  .md\:w-2\/3 {
    width: 66.666667%;
  }

  .md\:w-3\/12 {
    width: 25%;
  }

  .md\:w-4\/6 {
    width: 66.666667%;
  }

  .md\:w-5\/12 {
    width: 41.666667%;
  }

  .md\:w-\[40\%\] {
    width: 40%;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:max-w-2xl {
    max-width: 42rem;
  }

  .md\:max-w-4xl {
    max-width: 56rem;
  }

  .md\:max-w-5xl {
    max-width: 64rem;
  }

  .md\:max-w-none {
    max-width: none;
  }

  .md\:flex-1 {
    flex: 1 1 0%;
  }

  .md\:shrink-0 {
    flex-shrink: 0;
  }

  .md\:-translate-y-1\/2 {
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-6 {
    --tw-translate-y: 1.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:-translate-y-0 {
    --tw-translate-y: -0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-1\/2 {
    --tw-translate-y: 50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .md\:grid-cols-10 {
    grid-template-columns: repeat(10, minmax(0, 1fr));
  }

  .md\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-col {
    flex-direction: column;
  }

  .md\:flex-wrap {
    flex-wrap: wrap;
  }

  .md\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-normal {
    justify-content: normal;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:justify-end {
    justify-content: flex-end;
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:justify-evenly {
    justify-content: space-evenly;
  }

  .md\:gap-0 {
    gap: 0px;
  }

  .md\:gap-12 {
    gap: 3rem;
  }

  .md\:gap-16 {
    gap: 4rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:gap-8 {
    gap: 2rem;
  }

  .md\:gap-x-0 {
    -moz-column-gap: 0px;
         column-gap: 0px;
  }

  .md\:gap-x-12 {
    -moz-column-gap: 3rem;
         column-gap: 3rem;
  }

  .md\:gap-x-16 {
    -moz-column-gap: 4rem;
         column-gap: 4rem;
  }

  .md\:gap-x-32 {
    -moz-column-gap: 8rem;
         column-gap: 8rem;
  }

  .md\:gap-x-4 {
    -moz-column-gap: 1rem;
         column-gap: 1rem;
  }

  .md\:gap-x-8 {
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }

  .md\:gap-y-0 {
    row-gap: 0px;
  }

  .md\:gap-y-12 {
    row-gap: 3rem;
  }

  .md\:gap-y-16 {
    row-gap: 4rem;
  }

  .md\:gap-y-32 {
    row-gap: 8rem;
  }

  .md\:gap-y-4 {
    row-gap: 1rem;
  }

  .md\:gap-y-8 {
    row-gap: 2rem;
  }

  .md\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\:space-y-12 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(3rem * var(--tw-space-y-reverse));
  }

  .md\:space-y-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
  }

  .md\:space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }

  .md\:space-y-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse));
  }

  .md\:divide-none > :not([hidden]) ~ :not([hidden]) {
    border-style: none;
  }

  .md\:rounded-2xl {
    border-radius: 1rem;
  }

  .md\:rounded-lg {
    border-radius: 0.5rem;
  }

  .md\:rounded-l {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }

  .md\:rounded-r {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
  }

  .md\:border-y-0 {
    border-top-width: 0px;
    border-bottom-width: 0px;
  }

  .md\:border-b {
    border-bottom-width: 1px;
  }

  .md\:border-b-0 {
    border-bottom-width: 0px;
  }

  .md\:border-l {
    border-left-width: 1px;
  }

  .md\:border-l-2 {
    border-left-width: 2px;
  }

  .md\:border-r {
    border-right-width: 1px;
  }

  .md\:border-t {
    border-top-width: 1px;
  }

  .md\:border-t-0 {
    border-top-width: 0px;
  }

  .md\:border-none {
    border-style: none;
  }

  .md\:border-gray-300 {
    --tw-border-opacity: 1;
    border-color: rgb(214 211 209 / var(--tw-border-opacity));
  }

  .md\:border-gray-400 {
    --tw-border-opacity: 1;
    border-color: rgb(168 162 158 / var(--tw-border-opacity));
  }

  .md\:object-cover {
    -o-object-fit: cover;
       object-fit: cover;
  }

  .md\:p-0 {
    padding: 0px;
  }

  .md\:p-10 {
    padding: 2.5rem;
  }

  .md\:p-20 {
    padding: 5rem;
  }

  .md\:p-4 {
    padding: 1rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-14 {
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }

  .md\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .md\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .md\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .md\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .md\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .md\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .md\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .md\:pb-0 {
    padding-bottom: 0px;
  }

  .md\:pb-12 {
    padding-bottom: 3rem;
  }

  .md\:pb-16 {
    padding-bottom: 4rem;
  }

  .md\:pb-4 {
    padding-bottom: 1rem;
  }

  .md\:pb-8 {
    padding-bottom: 2rem;
  }

  .md\:pl-0 {
    padding-left: 0px;
  }

  .md\:pl-12 {
    padding-left: 3rem;
  }

  .md\:pr-12 {
    padding-right: 3rem;
  }

  .md\:pr-3 {
    padding-right: 0.75rem;
  }

  .md\:pr-8 {
    padding-right: 2rem;
  }

  .md\:pt-0 {
    padding-top: 0px;
  }

  .md\:pt-10 {
    padding-top: 2.5rem;
  }

  .md\:pt-12 {
    padding-top: 3rem;
  }

  .md\:pt-16 {
    padding-top: 4rem;
  }

  .md\:pt-2 {
    padding-top: 0.5rem;
  }

  .md\:pt-20 {
    padding-top: 5rem;
  }

  .md\:pt-24 {
    padding-top: 6rem;
  }

  .md\:pt-3 {
    padding-top: 0.75rem;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-center {
    text-align: center;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .md\:shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}

@media (min-width: 1024px) {
  .lg\:static {
    position: static;
  }

  .lg\:absolute {
    position: absolute;
  }

  .lg\:relative {
    position: relative;
  }

  .lg\:sticky {
    position: sticky;
  }

  .lg\:inset-0 {
    inset: 0px;
  }

  .lg\:inset-y-0 {
    top: 0px;
    bottom: 0px;
  }

  .lg\:bottom-\[68px\] {
    bottom: 68px;
  }

  .lg\:left-0 {
    left: 0px;
  }

  .lg\:left-auto {
    left: auto;
  }

  .lg\:right-0 {
    right: 0px;
  }

  .lg\:right-1\/2 {
    right: 50%;
  }

  .lg\:top-1\/2 {
    top: 50%;
  }

  .lg\:top-4 {
    top: 1rem;
  }

  .lg\:top-auto {
    top: auto;
  }

  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:order-3 {
    order: 3;
  }

  .lg\:order-first {
    order: -9999;
  }

  .lg\:order-last {
    order: 9999;
  }

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-10 {
    grid-column: span 10 / span 10;
  }

  .lg\:col-span-12 {
    grid-column: span 12 / span 12;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .lg\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .lg\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .lg\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .lg\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .lg\:col-start-1 {
    grid-column-start: 1;
  }

  .lg\:col-start-2 {
    grid-column-start: 2;
  }

  .lg\:col-start-3 {
    grid-column-start: 3;
  }

  .lg\:col-start-8 {
    grid-column-start: 8;
  }

  .lg\:col-start-9 {
    grid-column-start: 9;
  }

  .lg\:row-span-1 {
    grid-row: span 1 / span 1;
  }

  .lg\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .lg\:row-start-1 {
    grid-row-start: 1;
  }

  .lg\:row-start-2 {
    grid-row-start: 2;
  }

  .lg\:row-end-1 {
    grid-row-end: 1;
  }

  .lg\:row-end-2 {
    grid-row-end: 2;
  }

  .lg\:m-0 {
    margin: 0px;
  }

  .lg\:-mx-8 {
    margin-left: -2rem;
    margin-right: -2rem;
  }

  .lg\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .lg\:mx-52 {
    margin-left: 13rem;
    margin-right: 13rem;
  }

  .lg\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .lg\:my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .lg\:my-6 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .lg\:-mb-16 {
    margin-bottom: -4rem;
  }

  .lg\:-mb-24 {
    margin-bottom: -6rem;
  }

  .lg\:-ml-0 {
    margin-left: -0px;
  }

  .lg\:-mr-8 {
    margin-right: -2rem;
  }

  .lg\:-mr-96 {
    margin-right: -24rem;
  }

  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:mb-12 {
    margin-bottom: 3rem;
  }

  .lg\:mb-16 {
    margin-bottom: 4rem;
  }

  .lg\:mb-20 {
    margin-bottom: 5rem;
  }

  .lg\:mb-4 {
    margin-bottom: 1rem;
  }

  .lg\:mb-56 {
    margin-bottom: 14rem;
  }

  .lg\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .lg\:mb-8 {
    margin-bottom: 2rem;
  }

  .lg\:ml-0 {
    margin-left: 0px;
  }

  .lg\:ml-6 {
    margin-left: 1.5rem;
  }

  .lg\:ml-auto {
    margin-left: auto;
  }

  .lg\:mr-0 {
    margin-right: 0px;
  }

  .lg\:mr-auto {
    margin-right: auto;
  }

  .lg\:mt-0 {
    margin-top: 0px;
  }

  .lg\:mt-12 {
    margin-top: 3rem;
  }

  .lg\:mt-24 {
    margin-top: 6rem;
  }

  .lg\:mt-8 {
    margin-top: 2rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:grid {
    display: grid;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:aspect-\[3\/2\] {
    aspect-ratio: 3/2;
  }

  .lg\:aspect-\[5\/2\] {
    aspect-ratio: 5/2;
  }

  .lg\:aspect-\[8\/3\] {
    aspect-ratio: 8/3;
  }

  .lg\:aspect-auto {
    aspect-ratio: auto;
  }

  .lg\:aspect-square {
    aspect-ratio: 1 / 1;
  }

  .lg\:h-14 {
    height: 3.5rem;
  }

  .lg\:h-32 {
    height: 8rem;
  }

  .lg\:h-6 {
    height: 1.5rem;
  }

  .lg\:h-\[228px\] {
    height: 228px;
  }

  .lg\:h-\[720px\] {
    height: 720px;
  }

  .lg\:h-auto {
    height: auto;
  }

  .lg\:h-full {
    height: 100%;
  }

  .lg\:h-px {
    height: 1px;
  }

  .lg\:h-screen {
    height: 100vh;
  }

  .lg\:max-h-\[500px\] {
    max-height: 500px;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-1\/3 {
    width: 33.333333%;
  }

  .lg\:w-1\/4 {
    width: 25%;
  }

  .lg\:w-1\/5 {
    width: 20%;
  }

  .lg\:w-12 {
    width: 3rem;
  }

  .lg\:w-2\/3 {
    width: 66.666667%;
  }

  .lg\:w-32 {
    width: 8rem;
  }

  .lg\:w-4\/5 {
    width: 80%;
  }

  .lg\:w-5\/12 {
    width: 41.666667%;
  }

  .lg\:w-6 {
    width: 1.5rem;
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .lg\:w-full {
    width: 100%;
  }

  .lg\:w-px {
    width: 1px;
  }

  .lg\:max-w-2xl {
    max-width: 42rem;
  }

  .lg\:max-w-5xl {
    max-width: 64rem;
  }

  .lg\:max-w-7xl {
    max-width: 80rem;
  }

  .lg\:max-w-\[75\%\] {
    max-width: 75%;
  }

  .lg\:max-w-lg {
    max-width: 32rem;
  }

  .lg\:max-w-none {
    max-width: none;
  }

  .lg\:flex-1 {
    flex: 1 1 0%;
  }

  .lg\:translate-x-24 {
    --tw-translate-x: 6rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-12 {
    --tw-translate-y: 3rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-6 {
    --tw-translate-y: 1.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:scale-0 {
    --tw-scale-x: 0;
    --tw-scale-y: 0;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-flow-row-dense {
    grid-auto-flow: row dense;
  }

  .lg\:grid-flow-col-dense {
    grid-auto-flow: column dense;
  }

  .lg\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .lg\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .lg\:grid-rows-1 {
    grid-template-rows: repeat(1, minmax(0, 1fr));
  }

  .lg\:grid-rows-2 {
    grid-template-rows: repeat(2, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:flex-col {
    flex-direction: column;
  }

  .lg\:flex-wrap {
    flex-wrap: wrap;
  }

  .lg\:items-start {
    align-items: flex-start;
  }

  .lg\:items-end {
    align-items: flex-end;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:justify-start {
    justify-content: flex-start;
  }

  .lg\:justify-end {
    justify-content: flex-end;
  }

  .lg\:justify-center {
    justify-content: center;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:gap-10 {
    gap: 2.5rem;
  }

  .lg\:gap-12 {
    gap: 3rem;
  }

  .lg\:gap-14 {
    gap: 3.5rem;
  }

  .lg\:gap-16 {
    gap: 4rem;
  }

  .lg\:gap-20 {
    gap: 5rem;
  }

  .lg\:gap-24 {
    gap: 6rem;
  }

  .lg\:gap-4 {
    gap: 1rem;
  }

  .lg\:gap-8 {
    gap: 2rem;
  }

  .lg\:gap-x-0\.5 {
    -moz-column-gap: 0.125rem;
         column-gap: 0.125rem;
  }

  .lg\:gap-x-12 {
    -moz-column-gap: 3rem;
         column-gap: 3rem;
  }

  .lg\:gap-x-16 {
    -moz-column-gap: 4rem;
         column-gap: 4rem;
  }

  .lg\:gap-x-8 {
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }

  .lg\:gap-y-10 {
    row-gap: 2.5rem;
  }

  .lg\:gap-y-6 {
    row-gap: 1.5rem;
  }

  .lg\:gap-y-8 {
    row-gap: 2rem;
  }

  .lg\:space-x-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0px * var(--tw-space-x-reverse));
    margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-x-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.25rem * var(--tw-space-x-reverse));
    margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .lg\:space-y-12 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(3rem * var(--tw-space-y-reverse));
  }

  .lg\:space-y-16 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(4rem * var(--tw-space-y-reverse));
  }

  .lg\:space-y-20 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(5rem * var(--tw-space-y-reverse));
  }

  .lg\:space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }

  .lg\:space-y-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse));
  }

  .lg\:overflow-hidden {
    overflow: hidden;
  }

  .lg\:overflow-visible {
    overflow: visible;
  }

  .lg\:rounded {
    border-radius: 0.25rem;
  }

  .lg\:rounded-l {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }

  .lg\:rounded-r {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
  }

  .lg\:border-x {
    border-left-width: 1px;
    border-right-width: 1px;
  }

  .lg\:border-b {
    border-bottom-width: 1px;
  }

  .lg\:border-b-0 {
    border-bottom-width: 0px;
  }

  .lg\:border-l {
    border-left-width: 1px;
  }

  .lg\:border-r {
    border-right-width: 1px;
  }

  .lg\:border-r-0 {
    border-right-width: 0px;
  }

  .lg\:border-t {
    border-top-width: 1px;
  }

  .lg\:border-secondary-100 {
    --tw-border-opacity: 1;
    border-color: rgb(210 255 187 / var(--tw-border-opacity));
  }

  .lg\:object-contain {
    -o-object-fit: contain;
       object-fit: contain;
  }

  .lg\:p-0 {
    padding: 0px;
  }

  .lg\:p-10 {
    padding: 2.5rem;
  }

  .lg\:p-12 {
    padding: 3rem;
  }

  .lg\:p-16 {
    padding: 4rem;
  }

  .lg\:p-4 {
    padding: 1rem;
  }

  .lg\:p-6 {
    padding: 1.5rem;
  }

  .lg\:p-8 {
    padding: 2rem;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .lg\:px-14 {
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }

  .lg\:px-24 {
    padding-left: 6rem;
    padding-right: 6rem;
  }

  .lg\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .lg\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:px-px {
    padding-left: 1px;
    padding-right: 1px;
  }

  .lg\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .lg\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .lg\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .lg\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .lg\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .lg\:py-28 {
    padding-top: 7rem;
    padding-bottom: 7rem;
  }

  .lg\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .lg\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .lg\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .lg\:pb-0 {
    padding-bottom: 0px;
  }

  .lg\:pb-12 {
    padding-bottom: 3rem;
  }

  .lg\:pb-16 {
    padding-bottom: 4rem;
  }

  .lg\:pb-28 {
    padding-bottom: 7rem;
  }

  .lg\:pb-4 {
    padding-bottom: 1rem;
  }

  .lg\:pb-56 {
    padding-bottom: 14rem;
  }

  .lg\:pb-8 {
    padding-bottom: 2rem;
  }

  .lg\:pl-0 {
    padding-left: 0px;
  }

  .lg\:pl-14 {
    padding-left: 3.5rem;
  }

  .lg\:pl-16 {
    padding-left: 4rem;
  }

  .lg\:pl-4 {
    padding-left: 1rem;
  }

  .lg\:pl-8 {
    padding-left: 2rem;
  }

  .lg\:pr-0 {
    padding-right: 0px;
  }

  .lg\:pr-16 {
    padding-right: 4rem;
  }

  .lg\:pr-3 {
    padding-right: 0.75rem;
  }

  .lg\:pr-4 {
    padding-right: 1rem;
  }

  .lg\:pr-8 {
    padding-right: 2rem;
  }

  .lg\:pt-0 {
    padding-top: 0px;
  }

  .lg\:pt-14 {
    padding-top: 3.5rem;
  }

  .lg\:pt-16 {
    padding-top: 4rem;
  }

  .lg\:pt-2 {
    padding-top: 0.5rem;
  }

  .lg\:pt-20 {
    padding-top: 5rem;
  }

  .lg\:pt-24 {
    padding-top: 6rem;
  }

  .lg\:pt-4 {
    padding-top: 1rem;
  }

  .lg\:pt-40 {
    padding-top: 10rem;
  }

  .lg\:pt-48 {
    padding-top: 12rem;
  }

  .lg\:pt-6 {
    padding-top: 1.5rem;
  }

  .lg\:pt-8 {
    padding-top: 2rem;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-center {
    text-align: center;
  }

  .lg\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .lg\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .lg\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .lg\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .lg\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .lg\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .lg\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .lg\:leading-snug {
    line-height: 1.375;
  }

  .lg\:text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(120 113 108 / var(--tw-text-opacity));
  }

  .lg\:text-secondary-500 {
    --tw-text-opacity: 1;
    color: rgb(27 255 0 / var(--tw-text-opacity));
  }

  .lg\:opacity-0 {
    opacity: 0;
  }

  .lg\:hover\:-translate-y-6:hover {
    --tw-translate-y: -1.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:hover\:translate-y-0:hover {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:hover\:translate-y-6:hover {
    --tw-translate-y: 1.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:hover\:border-primary-500:hover {
    --tw-border-opacity: 1;
    border-color: rgb(243 155 78 / var(--tw-border-opacity));
  }

  .group:hover .lg\:group-hover\:bottom-0 {
    bottom: 0px;
  }

  .group:hover .lg\:group-hover\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .group:hover .lg\:group-hover\:opacity-100 {
    opacity: 1;
  }
}

@media (min-width: 1280px) {
  .xl\:static {
    position: static;
  }

  .xl\:absolute {
    position: absolute;
  }

  .xl\:sticky {
    position: sticky;
  }

  .xl\:inset-0 {
    inset: 0px;
  }

  .xl\:left-1\/2 {
    left: 50%;
  }

  .xl\:top-8 {
    top: 2rem;
  }

  .xl\:z-10 {
    z-index: 10;
  }

  .xl\:order-1 {
    order: 1;
  }

  .xl\:order-first {
    order: -9999;
  }

  .xl\:order-last {
    order: 9999;
  }

  .xl\:col-auto {
    grid-column: auto;
  }

  .xl\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .xl\:col-span-10 {
    grid-column: span 10 / span 10;
  }

  .xl\:col-span-11 {
    grid-column: span 11 / span 11;
  }

  .xl\:col-span-12 {
    grid-column: span 12 / span 12;
  }

  .xl\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .xl\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .xl\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .xl\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .xl\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .xl\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .xl\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .xl\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .xl\:col-start-1 {
    grid-column-start: 1;
  }

  .xl\:col-start-2 {
    grid-column-start: 2;
  }

  .xl\:col-start-4 {
    grid-column-start: 4;
  }

  .xl\:col-start-7 {
    grid-column-start: 7;
  }

  .xl\:col-start-8 {
    grid-column-start: 8;
  }

  .xl\:col-end-1 {
    grid-column-end: 1;
  }

  .xl\:row-span-1 {
    grid-row: span 1 / span 1;
  }

  .xl\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .xl\:row-span-3 {
    grid-row: span 3 / span 3;
  }

  .xl\:row-span-4 {
    grid-row: span 4 / span 4;
  }

  .xl\:row-start-1 {
    grid-row-start: 1;
  }

  .xl\:row-start-2 {
    grid-row-start: 2;
  }

  .xl\:row-end-2 {
    grid-row-end: 2;
  }

  .xl\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .xl\:mb-0 {
    margin-bottom: 0px;
  }

  .xl\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .xl\:ml-0 {
    margin-left: 0px;
  }

  .xl\:mr-0 {
    margin-right: 0px;
  }

  .xl\:mt-16 {
    margin-top: 4rem;
  }

  .xl\:mt-9 {
    margin-top: 2.25rem;
  }

  .xl\:mb-20 {
    margin-bottom: 5rem;
  }

  .xl\:ml-20 {
    margin-left: 5rem;
  }

  .xl\:block {
    display: block;
  }

  .xl\:inline {
    display: inline;
  }

  .xl\:flex {
    display: flex;
  }

  .xl\:grid {
    display: grid;
  }

  .xl\:hidden {
    display: none;
  }

  .xl\:aspect-\[5\/4\] {
    aspect-ratio: 5/4;
  }

  .xl\:aspect-video {
    aspect-ratio: 16 / 9;
  }

  .xl\:h-12 {
    height: 3rem;
  }

  .xl\:h-14 {
    height: 3.5rem;
  }

  .xl\:h-\[292px\] {
    height: 292px;
  }

  .xl\:h-\[540px\] {
    height: 540px;
  }

  .xl\:h-\[800px\] {
    height: 800px;
  }

  .xl\:h-full {
    height: 100%;
  }

  .xl\:w-1\/2 {
    width: 50%;
  }

  .xl\:w-1\/3 {
    width: 33.333333%;
  }

  .xl\:w-1\/4 {
    width: 25%;
  }

  .xl\:w-1\/6 {
    width: 16.666667%;
  }

  .xl\:w-2\/3 {
    width: 66.666667%;
  }

  .xl\:w-auto {
    width: auto;
  }

  .xl\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .xl\:w-full {
    width: 100%;
  }

  .xl\:w-2\/5 {
    width: 40%;
  }

  .xl\:max-w-7xl {
    max-width: 80rem;
  }

  .xl\:max-w-\[66\%\] {
    max-width: 66%;
  }

  .xl\:max-w-none {
    max-width: none;
  }

  .xl\:flex-1 {
    flex: 1 1 0%;
  }

  .xl\:grid-flow-col {
    grid-auto-flow: column;
  }

  .xl\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .xl\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .xl\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .xl\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .xl\:grid-cols-none {
    grid-template-columns: none;
  }

  .xl\:grid-rows-1 {
    grid-template-rows: repeat(1, minmax(0, 1fr));
  }

  .xl\:grid-rows-2 {
    grid-template-rows: repeat(2, minmax(0, 1fr));
  }

  .xl\:flex-row {
    flex-direction: row;
  }

  .xl\:flex-col {
    flex-direction: column;
  }

  .xl\:flex-wrap {
    flex-wrap: wrap;
  }

  .xl\:items-start {
    align-items: flex-start;
  }

  .xl\:items-center {
    align-items: center;
  }

  .xl\:justify-start {
    justify-content: flex-start;
  }

  .xl\:justify-end {
    justify-content: flex-end;
  }

  .xl\:justify-between {
    justify-content: space-between;
  }

  .xl\:gap-0 {
    gap: 0px;
  }

  .xl\:gap-10 {
    gap: 2.5rem;
  }

  .xl\:gap-12 {
    gap: 3rem;
  }

  .xl\:gap-16 {
    gap: 4rem;
  }

  .xl\:gap-4 {
    gap: 1rem;
  }

  .xl\:gap-8 {
    gap: 2rem;
  }

  .xl\:gap-x-0 {
    -moz-column-gap: 0px;
         column-gap: 0px;
  }

  .xl\:gap-x-12 {
    -moz-column-gap: 3rem;
         column-gap: 3rem;
  }

  .xl\:gap-x-16 {
    -moz-column-gap: 4rem;
         column-gap: 4rem;
  }

  .xl\:gap-x-32 {
    -moz-column-gap: 8rem;
         column-gap: 8rem;
  }

  .xl\:gap-x-4 {
    -moz-column-gap: 1rem;
         column-gap: 1rem;
  }

  .xl\:gap-x-8 {
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }

  .xl\:gap-y-0 {
    row-gap: 0px;
  }

  .xl\:gap-y-12 {
    row-gap: 3rem;
  }

  .xl\:gap-y-16 {
    row-gap: 4rem;
  }

  .xl\:gap-y-32 {
    row-gap: 8rem;
  }

  .xl\:gap-y-4 {
    row-gap: 1rem;
  }

  .xl\:gap-y-8 {
    row-gap: 2rem;
  }

  .xl\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .xl\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .xl\:space-y-12 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(3rem * var(--tw-space-y-reverse));
  }

  .xl\:space-y-16 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(4rem * var(--tw-space-y-reverse));
  }

  .xl\:rounded {
    border-radius: 0.25rem;
  }

  .xl\:p-10 {
    padding: 2.5rem;
  }

  .xl\:p-8 {
    padding: 2rem;
  }

  .xl\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .xl\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .xl\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .xl\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .xl\:px-32 {
    padding-left: 8rem;
    padding-right: 8rem;
  }

  .xl\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .xl\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .xl\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .xl\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .xl\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .xl\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .xl\:py-48 {
    padding-top: 12rem;
    padding-bottom: 12rem;
  }

  .xl\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .xl\:px-14 {
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }

  .xl\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .xl\:pb-10 {
    padding-bottom: 2.5rem;
  }

  .xl\:pl-12 {
    padding-left: 3rem;
  }

  .xl\:pl-20 {
    padding-left: 5rem;
  }

  .xl\:pr-12 {
    padding-right: 3rem;
  }

  .xl\:pr-20 {
    padding-right: 5rem;
  }

  .xl\:pt-0 {
    padding-top: 0px;
  }

  .xl\:pt-52 {
    padding-top: 13rem;
  }

  .xl\:text-left {
    text-align: left;
  }

  .xl\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .xl\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .xl\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .xl\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .xl\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1536px) {
  .\32xl\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .\32xl\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .\32xl\:mb-12 {
    margin-bottom: 3rem;
  }

  .\32xl\:mt-12 {
    margin-top: 3rem;
  }

  .\32xl\:aspect-\[4\/2\] {
    aspect-ratio: 4/2;
  }

  .\32xl\:-translate-y-4 {
    --tw-translate-y: -1rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .\32xl\:translate-x-44 {
    --tw-translate-x: 11rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .\32xl\:space-x-16 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(4rem * var(--tw-space-x-reverse));
    margin-left: calc(4rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .\32xl\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .\32xl\:text-7xl {
    font-size: 4.5rem;
    line-height: 1;
  }
}

.dark\:divide-gray-300:where(.dark, .dark *) > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(214 211 209 / var(--tw-divide-opacity));
}

.dark\:divide-white\/20:where(.dark, .dark *) > :not([hidden]) ~ :not([hidden]) {
  border-color: rgb(255 255 255 / 0.2);
}

.dark\:border-gray-200:where(.dark, .dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(231 229 228 / var(--tw-border-opacity));
}

.dark\:border-gray-200\/10:where(.dark, .dark *) {
  border-color: rgb(231 229 228 / 0.1);
}

.dark\:border-gray-600:where(.dark, .dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(87 83 78 / var(--tw-border-opacity));
}

.dark\:border-gray-700:where(.dark, .dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(68 64 60 / var(--tw-border-opacity));
}

.dark\:border-white\/20:where(.dark, .dark *) {
  border-color: rgb(255 255 255 / 0.2);
}

.dark\:border-white\/5:where(.dark, .dark *) {
  border-color: rgb(255 255 255 / 0.05);
}

.dark\:bg-black\/30:where(.dark, .dark *) {
  background-color: rgb(0 0 0 / 0.3);
}

.dark\:bg-gray-300:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(214 211 209 / var(--tw-bg-opacity));
}

.dark\:bg-gray-500:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(120 113 108 / var(--tw-bg-opacity));
}

.dark\:bg-gray-600:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(87 83 78 / var(--tw-bg-opacity));
}

.dark\:bg-gray-700:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(68 64 60 / var(--tw-bg-opacity));
}

.dark\:bg-gray-900\/40:where(.dark, .dark *) {
  background-color: rgb(28 25 23 / 0.4);
}

.dark\:bg-primary-500:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(243 155 78 / var(--tw-bg-opacity));
}

.dark\:bg-primary-600:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(238 116 2 / var(--tw-bg-opacity));
}

.dark\:bg-primary-800:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(167 75 2 / var(--tw-bg-opacity));
}

.dark\:bg-white:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.dark\:bg-white\/10:where(.dark, .dark *) {
  background-color: rgb(255 255 255 / 0.1);
}

.dark\:bg-white\/5:where(.dark, .dark *) {
  background-color: rgb(255 255 255 / 0.05);
}

.dark\:text-gray-100:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(245 245 244 / var(--tw-text-opacity));
}

.dark\:text-gray-200:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(231 229 228 / var(--tw-text-opacity));
}

.dark\:text-gray-300:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(214 211 209 / var(--tw-text-opacity));
}

.dark\:text-gray-400:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(168 162 158 / var(--tw-text-opacity));
}

.dark\:text-gray-50:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(250 250 249 / var(--tw-text-opacity));
}

.dark\:text-gray-500:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(120 113 108 / var(--tw-text-opacity));
}

.dark\:text-gray-600:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(87 83 78 / var(--tw-text-opacity));
}

.dark\:text-primary-50:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 243 240 / var(--tw-text-opacity));
}

.dark\:text-primary-500:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 155 78 / var(--tw-text-opacity));
}

.dark\:text-primary-highlight:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(250 219 201 / var(--tw-text-opacity));
}

.dark\:text-red-600:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}

.dark\:text-white:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark\:placeholder\:text-gray-300:where(.dark, .dark *)::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(214 211 209 / var(--tw-text-opacity));
}

.dark\:placeholder\:text-gray-300:where(.dark, .dark *)::placeholder {
  --tw-text-opacity: 1;
  color: rgb(214 211 209 / var(--tw-text-opacity));
}

.dark\:hover\:bg-gray-700:hover:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(68 64 60 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-primary-600:hover:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(238 116 2 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-primary-700:hover:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(204 95 2 / var(--tw-bg-opacity));
}

.dark\:hover\:text-gray-100:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(245 245 244 / var(--tw-text-opacity));
}

.dark\:hover\:text-gray-200:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(231 229 228 / var(--tw-text-opacity));
}

.dark\:hover\:text-white:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.group\/card:hover .dark\:group-hover\/card\:bg-gray-700:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(68 64 60 / var(--tw-bg-opacity));
}

.group\/card:hover .dark\:group-hover\/card\:text-primary-highlight:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(250 219 201 / var(--tw-text-opacity));
}

.group:hover .dark\:group-hover\:text-primary-400:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(245 180 125 / var(--tw-text-opacity));
}

.group:hover .dark\:group-hover\:text-primary-highlight:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(250 219 201 / var(--tw-text-opacity));
}

.dark\:prose-h3\:text-white :is(:where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *))):where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark\:prose-p\:text-gray-100 :is(:where(p):not(:where([class~="not-prose"],[class~="not-prose"] *))):where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(245 245 244 / var(--tw-text-opacity));
}

.dark\:prose-p\:text-gray-300 :is(:where(p):not(:where([class~="not-prose"],[class~="not-prose"] *))):where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(214 211 209 / var(--tw-text-opacity));
}

.dark\:prose-strong\:text-secondary-500 :is(:where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *))):where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(27 255 0 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .dark\:md\:border-gray-600:where(.dark, .dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(87 83 78 / var(--tw-border-opacity));
  }

  .dark\:md\:border-gray-900\/20:where(.dark, .dark *) {
    border-color: rgb(28 25 23 / 0.2);
  }

  .dark\:md\:border-white\/5:where(.dark, .dark *) {
    border-color: rgb(255 255 255 / 0.05);
  }
}
