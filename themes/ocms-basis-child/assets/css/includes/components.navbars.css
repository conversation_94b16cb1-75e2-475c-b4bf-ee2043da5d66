/* .navbar { @apply bg-gray-100; } */
/* .navbar-top { @apply border-b-2 border-gray-300 py-4 md:py-3; } */
/* .navbar-bottom { @apply border-t-2 border-b border-gray-300 py-3; } */
/* .navbar .navbar-row { @apply md:gap-x-8; } */


/* .navbar .logo { @apply py-4 md:py-3; } */
/* .navbar .logo img { @apply max-h-8 lg:max-h-10 object-contain object-left; } */


/* .navbar-buttons { @apply hidden md:block; } */
/* .navbar-buttons a.link { @apply hidden md:inline-block uppercase leading-none text-gray-600 hover:text-gray-800; } */
/* .navbar-buttons a.button { @apply hidden md:flex py-3 px-8 rounded-md border border-gray-500 hover:border-gray-800; } */


/* .navbar .contact-info address { @apply not-italic flex gap-x-8; } */
/* .navbar .contact-info address a { @apply inline-flex items-center justify-center md:justify-start h-8 w-8 md:h-auto md:w-auto rounded-full md:rounded-none bg-gray-700 md:bg-transparent text-white md:text-gray-600 hover:text-gray-800 leading-none; } */
/* .navbar .contact-info address a span { @apply hidden md:block ml-1.5; } */


/* .navbar-top .contact-info address { @apply not-italic flex gap-x-8; } */
 .navbar-top .contact-info address a { @apply inline-flex items-center text-black hover:text-primary leading-none; }
/* .navbar-top .contact-info address a span { @apply ml-1.5; } */


/* .navbar .search { @apply border-l border-gray-300 pl-4 md:pl-8; } */
/* .navbar .search-icon { @apply text-xl leading-none text-gray-400 hover:text-gray-600; } */


/* .navbar-top .social-media a { @apply text-lg text-gray-600 hover:text-gray-800 leading-none; } */


/* #navbar-5 .navbar-row { @apply gap-x-6 lg:gap-x-16; } */
/* #navbar-8 .navbar .navbar-row { @apply gap-x-4; } */
/* #navbar-9 { @apply bg-gray-50; } */
/* #navbar-9 .navbar { @apply bg-gray-50 py-4; } */
/* #navbar-9 .navbar .navbar-row { @apply gap-x-4; } */
/* #navbar-10 .navbar .navbar-row { @apply gap-x-4; } */