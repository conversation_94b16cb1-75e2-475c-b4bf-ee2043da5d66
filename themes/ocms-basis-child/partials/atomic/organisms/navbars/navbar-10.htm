[staticMenu mainMenu]
code = "main-menu"

[staticMenu topMenu]
code = "top-menu"
==
{% set mainmenuItems = mainMenu.resetMenu(navbar.mainmenu) %}
{% set topmenuItems = topMenu.resetMenu(navbar.topmenu) %}

<header data-section="navbar" data-name="navbar-10" class="relative z-[51]" x-data="{ open: false }">
    <div class="px-4 md:px-6 lg:px-8 py-2 bg-primary-50 hidden xs:block">
        <div class="flex justify-end lg:justify-between items-center gap-x-8 container">
            <div class="hidden lg:block">
                {% partial 'atomic/molecules/navbar/nav-top' %}
            </div>
            <div class="flex items-center gap-x-8">
                <div class="">
                    {% partial 'atomic/molecules/navbar/social-media' %}
                </div>
                {% partial 'atomic/molecules/navbar/contact-links' %}
            </div>
        </div>
    </div>

    {% partial 'atomic/organisms/navbars/navbar-bg-wrap' body %}
        <div class="container flex items-center gap-x-6 lg:gap-x-8">
            <div class="relative shrink-0 flex items-center">
                {% partial 'atomic/atoms/navbar/logo' %}
            </div>
            <div class="hidden lg:flex items-center ml-auto">
                {% partial 'atomic/molecules/navbar/nav' %}
            </div>
            <div class="hidden xs:block border-gray-400 pr-6 border-r lg:pl-8 lg:pr-0 lg:border-l lg:border-r-0 ml-auto lg:ml-0 text-xl lg:text-base">
                <div x-data="openClose" class="text-gray-400 lg:text-gray-500">{% component 'searchInput' %}</div>
            </div>
            <div class="ml-auto xs:ml-0 lg:hidden">
                {% partial 'atomic/atoms/navbar/bars' icon="bars-staggered" %}
            </div>
        </div>
    {% endpartial %}

    {% partial 'atomic/organisms/navbars/mobile' %}

</header>
