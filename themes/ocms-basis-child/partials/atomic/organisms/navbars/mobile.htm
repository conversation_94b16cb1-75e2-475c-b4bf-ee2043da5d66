<div
    id="navbar-mobile"
    class="fixed inset-0 z-50"
    x-show="open"
    x-cloak>

    <div
        class="absolute inset-0 bg-gray-600/50"
        x-show="open"
        @click="open = false"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"></div>

    <div
        class="h-full w-[98%] md:w-[40%] bg-primary-50 rounded-l-3xl overflow-y-auto ml-auto relative z-20"
        x-trap.noscroll.noautofocus="open"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 translate-x-full"
        x-transition:enter-end="opacity-100 translate-x-0"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100 translate-x-0"
        x-transition:leave-end="opacity-0 translate-x-full"
        x-show="open">

        <div class="flex flex-col h-full">

            <div class="flex-1 p-6 space-y-8 bg-white">
                <div class="grid grid-cols-12 gap-4 relative items-start">
                    <div class="col-span-10">
                        {% partial 'atomic/molecules/navbar/nav-mobile' %}
                        {% if cta %}
                            <div class="mt-6">
                                {% partial 'atomic/atoms/navbar/cta' %}
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-span-2 justify-self-end sticky top-6">
                        {% partial 'atomic/atoms/navbar/close' %}
                    </div>
                </div>
            </div>

            <div class="p-6 space-y-8 border-t">
                <div>
                    {% partial 'atomic/atoms/navbar/logo' %}
                </div>
                <div class="">
                    {% partial 'ui/contact_info_basic' %}
                </div>
                <div class="text-primary">
                    {% partial 'atomic/molecules/navbar/social-media' %}
                </div>
            </div>

        </div>
    </div>

</div>
