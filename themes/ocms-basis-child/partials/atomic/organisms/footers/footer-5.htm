[staticMenu footerMenu]
code = "footer-menu"

[staticMenu footerMenu2]
code = "footer-menu"
==
{% set footerMenu1Items = footerMenu.resetMenu(footer.footer_menu_1) %}
{% set footerMenu2Items = footerMenu2.resetMenu(footer.footer_menu_2) %}

{% partial 'atomic/organisms/footers/footer-wrap' name="footer-5" body %}
    <div class="container">
        <div class="py-8 space-y-8 md:space-y-0 md:grid md:grid-cols-12 md:gap-8 xl:gap-12">

            <div class="space-y-8 md:pr-8 md:border-r dark:border-white/20 md:pt-3 md:col-span-6 lg:col-span-4 2xl:col-span-5">
                <div class="flex items-center shrink-0 w-full md:w-auto justify-start md:justify-normal">
                    {% partial 'atomic/atoms/footer/logo' %}
                </div>

                <div class="prose prose-sm prose-primary dark:prose-primary_inverted max-w-none">
                    {{ footer.text | content }}
                </div>

                {% if socials %}
                    <div class="flex">
                        {% partial 'atomic/molecules/footer/social-media' %}
                    </div>
                {% endif %}
            </div>

            <div class="border-t dark:border-white/20 md:border-t-0 pt-8 md:pr-8 md:border-r dark:border-white/20 md:pt-3 md:col-span-3 lg:col-span-2">
                <div class="space-y-3">
                    {% partial 'atomic/atoms/footer/heading' text=footer.footer_menu_1_title %}
                    {% partial 'atomic/molecules/footer/nav' menuItems=footerMenu1Items %}
                </div>
            </div>

            <div class="border-t dark:border-white/20 md:border-t-0 pt-8 md:pr-8 md:border-r dark:border-white/20 md:pt-3 md:col-span-3 lg:col-span-2">
                <div class="space-y-3">
                    {% partial 'atomic/atoms/footer/heading' text=footer.footer_menu_2_title %}
                    {% partial 'atomic/molecules/footer/nav' menuItems=footerMenu2Items %}
                </div>
            </div>

            <div class="border-t dark:border-white/20 md:border-t-0 pt-8 dark:border-white/20 md:pt-3 md:col-span-8 lg:col-span-4 2xl:col-span-3">
                <div class="space-y-3">
                    {% partial 'atomic/atoms/footer/heading' text="Contact" %}
                    <div>
                        {% partial 'atomic/molecules/footer/contact-info' %}
                    </div>
                </div>
            </div>

        </div>

        {% if avg.tos_slug or avg.tos_media or avg.privacy_slug or avg.privacy_media %}
            <div class="pt-4 pb-8 md:border-b md:border-gray-300 dark:md:border-gray-600 lg:hidden">
                <div class="space-y-3 text-black md:space-y-0 md:flex md:justify-between md:items-center md:text-sm">
                    <div>
                        {% partial 'atomic/atoms/footer/tos' %}
                    </div>
                    <div>
                        {% partial 'atomic/atoms/footer/privacy' %}
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
    <div class="py-4 bg-primary-50 border-t dark:bg-gray-900/40 dark:md:border-gray-600">
        <div class="container">
            <div class="flex justify-between items-center text-sm text-gray-500">
                {% if avg.tos_slug or avg.tos_media or avg.privacy_slug or avg.privacy_media %}
                    <div class="hidden lg:flex space-x-4 items-center text-sm">
                        <div>
                            {% partial 'atomic/atoms/footer/tos' %}
                        </div>
                        <div>
                            {% partial 'atomic/atoms/footer/privacy' %}
                        </div>
                    </div>
                {% endif %}

                <div class="flex flex-col items-center gap-2 w-full md:flex-row md:justify-between lg:w-auto dark:text-gray-100">
                    {% partial 'atomic/atoms/footer/copyright' %}
                    {% partial 'atomic/atoms/footer/powered-by' %}
                </div>
            </div>
        </div>
    </div>
{% endpartial %}
