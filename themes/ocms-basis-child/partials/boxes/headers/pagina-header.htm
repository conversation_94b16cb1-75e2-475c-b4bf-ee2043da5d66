<section 
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %} 
    data-name="pagina-header" 
    data-category="headers" 
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};">
    
	<div class="relative md:container md:py-8 xl:py-12">

        <div class="overflow-hidden h-[320px] md:h-[450px] xl:h-[540px] md:rounded-2xl">
            {% partial 'atomic/atoms/media/image' img=box.img resize_w='1920' class='w-full h-full object-cover' %}
        </div>

        <div class="absolute inset-x-0 bottom-0 translate-y-1/2 h-fit md:inset-0 md:translate-y-0 md:flex md:items-end md:justify-start md:h-full md:pl-12 md:pb-20 xl:pl-20 xl:pb-32">
            <div class="bg-primary-50 rounded-2xl p-8 mx-4 md:max-w-2xl md:w-2/3 xl:w-1/2">
                <div class="space-y-4 lg:space-y-8">
                    <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                        {% partial 'atomic/atoms/headings/header-h1' text=box.title %}
                    </div>
                    <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                        {{ box.content | content }}
                    </div>
                    {% if box.buttons %}
                        <div class="not-prose ">
                            {% partial 'atomic/molecules/buttons' buttons=box.buttons class="buttons-wrap" %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

    </div>
	
</section>
